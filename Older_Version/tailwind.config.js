module.exports = {
  content: ['./client/src/**/*.{tsx,ts}', './client/index.html'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        adobe: {
          bg: {
            primary: 'var(--adobe-bg-primary)',
            secondary: 'var(--adobe-bg-secondary)',
            tertiary: 'var(--adobe-bg-tertiary)',
          },
          text: {
            primary: 'var(--adobe-text-primary)',
            secondary: 'var(--adobe-text-secondary)',
          },
          border: 'var(--adobe-border)',
          accent: 'var(--adobe-accent)',
          error: 'var(--adobe-error)',
          warning: 'var(--adobe-warning)',
          success: 'var(--adobe-success)',
        },
      },
    },
  },
  plugins: [],
};