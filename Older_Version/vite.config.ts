import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  base: './',                              // relative paths for CEP
  root: 'client',                          // source folder
  build: {
    outDir: resolve(__dirname, 'dist'),    // 1. build into /dist
    emptyOutDir: true,                     // 2. clean before every build
    rollupOptions: {
      output: {
        entryFileNames: 'index.js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
      },
    },
  },
  server: { port: 3000, strictPort: true },
});