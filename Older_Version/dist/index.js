const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./assets/angular-html-LfdN0zeE.js","./assets/html-C2L_23MC.js","./assets/javascript-ySlJ1b_l.js","./assets/css-BPhBrDlE.js","./assets/angular-ts-CKsD7JZE.js","./assets/scss-C31hgJw-.js","./assets/apl-BBq3IX1j.js","./assets/xml-e3z08dGr.js","./assets/java-xI-RfyKK.js","./assets/json-BQoSv7ci.js","./assets/astro-CqkE3fuf.js","./assets/typescript-Dj6nwHGl.js","./assets/postcss-B3ZDOciz.js","./assets/blade-a8OxSdnT.js","./assets/sql-COK4E0Yg.js","./assets/bsl-Dgyn0ogV.js","./assets/sdbl-BLhTXw86.js","./assets/cairo--RitsXJZ.js","./assets/python-DhUJRlN_.js","./assets/cobol-PTqiYgYu.js","./assets/coffee-dyiR41kL.js","./assets/cpp-BksuvNSY.js","./assets/regexp-DWJ3fJO_.js","./assets/glsl-DBO2IWDn.js","./assets/c-C3t2pwGQ.js","./assets/crystal-DtDmRg-F.js","./assets/shellscript-atvbtKCR.js","./assets/edge-D5gP-w-T.js","./assets/html-derivative-CSfWNPLT.js","./assets/elixir-CLiX3zqd.js","./assets/elm-CmHSxxaM.js","./assets/erb-BYTLMnw6.js","./assets/ruby-DeZ3UC14.js","./assets/haml-B2EZWmdv.js","./assets/graphql-cDcHW_If.js","./assets/jsx-BAng5TT0.js","./assets/tsx-B6W0miNI.js","./assets/lua-CvWAzNxB.js","./assets/yaml-CVw76BM1.js","./assets/fortran-fixed-form-TqA4NnZg.js","./assets/fortran-free-form-DKXYxT9g.js","./assets/fsharp-XplgxFYe.js","./assets/markdown-UIAJJxZW.js","./assets/gdresource-BHYsBjWJ.js","./assets/gdshader-SKMF96pI.js","./assets/gdscript-DfxzS6Rs.js","./assets/git-commit-i4q6IMui.js","./assets/diff-BgYniUM_.js","./assets/git-rebase-B-v9cOL2.js","./assets/glimmer-js-D-cwc0-E.js","./assets/glimmer-ts-pgjy16dm.js","./assets/hack-D1yCygmZ.js","./assets/handlebars-BQGss363.js","./assets/http-FRrOvY1W.js","./assets/hxml-TIA70rKU.js","./assets/haxe-C5wWYbrZ.js","./assets/imba-bv_oIlVt.js","./assets/jinja-DGy0s7-h.js","./assets/jison-BqZprYcd.js","./assets/julia-BBuGR-5E.js","./assets/r-CwjWoCRV.js","./assets/latex-C-cWTeAZ.js","./assets/tex-rYs2v40G.js","./assets/liquid-D3W5UaiH.js","./assets/marko-z0MBrx5-.js","./assets/less-BfCpw3nA.js","./assets/mdc-DB_EDNY_.js","./assets/nginx-D_VnBJ67.js","./assets/nim-ZlGxZxc3.js","./assets/perl-CHQXSrWU.js","./assets/php-B5ebYQev.js","./assets/pug-CM9l7STV.js","./assets/qml-D8XfuvdV.js","./assets/razor-CNLDkMZG.js","./assets/csharp-D9R-vmeu.js","./assets/rst-4NLicBqY.js","./assets/cmake-DbXoA79R.js","./assets/sas-BmTFh92c.js","./assets/shaderlab-B7qAK45m.js","./assets/hlsl-ifBTmRxC.js","./assets/shellsession-C_rIy8kc.js","./assets/soy-C-lX7w71.js","./assets/sparql-bYkjHRlG.js","./assets/turtle-BMR_PYu6.js","./assets/stata-DorPZHa4.js","./assets/svelte-MSaWC3Je.js","./assets/templ-dwX3ZSMB.js","./assets/go-B1SYOhNW.js","./assets/ts-tags-CipyTH0X.js","./assets/twig-NC5TFiHP.js","./assets/vue-BuYVFjOK.js","./assets/vue-html-xdeiXROB.js","./assets/xsl-Dd0NUgwM.js"])))=>i.map(i=>d[i]);
var cf=Object.defineProperty;var df=(e,t,n)=>t in e?cf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var x=(e,t,n)=>df(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function rc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var oc={exports:{}},ai={},ic={exports:{}},M={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jr=Symbol.for("react.element"),pf=Symbol.for("react.portal"),ff=Symbol.for("react.fragment"),mf=Symbol.for("react.strict_mode"),hf=Symbol.for("react.profiler"),gf=Symbol.for("react.provider"),_f=Symbol.for("react.context"),yf=Symbol.for("react.forward_ref"),vf=Symbol.for("react.suspense"),Ef=Symbol.for("react.memo"),Sf=Symbol.for("react.lazy"),Ea=Symbol.iterator;function wf(e){return e===null||typeof e!="object"?null:(e=Ea&&e[Ea]||e["@@iterator"],typeof e=="function"?e:null)}var lc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},sc=Object.assign,ac={};function zn(e,t,n){this.props=e,this.context=t,this.refs=ac,this.updater=n||lc}zn.prototype.isReactComponent={};zn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};zn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function uc(){}uc.prototype=zn.prototype;function ms(e,t,n){this.props=e,this.context=t,this.refs=ac,this.updater=n||lc}var hs=ms.prototype=new uc;hs.constructor=ms;sc(hs,zn.prototype);hs.isPureReactComponent=!0;var Sa=Array.isArray,cc=Object.prototype.hasOwnProperty,gs={current:null},dc={key:!0,ref:!0,__self:!0,__source:!0};function pc(e,t,n){var r,o={},i=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)cc.call(t,r)&&!dc.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(s===1)o.children=n;else if(1<s){for(var a=Array(s),u=0;u<s;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)o[r]===void 0&&(o[r]=s[r]);return{$$typeof:jr,type:e,key:i,ref:l,props:o,_owner:gs.current}}function xf(e,t){return{$$typeof:jr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function _s(e){return typeof e=="object"&&e!==null&&e.$$typeof===jr}function kf(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var wa=/\/+/g;function Ii(e,t){return typeof e=="object"&&e!==null&&e.key!=null?kf(""+e.key):t.toString(36)}function mo(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case jr:case pf:l=!0}}if(l)return l=e,o=o(l),e=r===""?"."+Ii(l,0):r,Sa(o)?(n="",e!=null&&(n=e.replace(wa,"$&/")+"/"),mo(o,t,n,"",function(u){return u})):o!=null&&(_s(o)&&(o=xf(o,n+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(wa,"$&/")+"/")+e)),t.push(o)),1;if(l=0,r=r===""?".":r+":",Sa(e))for(var s=0;s<e.length;s++){i=e[s];var a=r+Ii(i,s);l+=mo(i,t,n,a,o)}else if(a=wf(e),typeof a=="function")for(e=a.call(e),s=0;!(i=e.next()).done;)i=i.value,a=r+Ii(i,s++),l+=mo(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function Wr(e,t,n){if(e==null)return e;var r=[],o=0;return mo(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Cf(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Se={current:null},ho={transition:null},Pf={ReactCurrentDispatcher:Se,ReactCurrentBatchConfig:ho,ReactCurrentOwner:gs};function fc(){throw Error("act(...) is not supported in production builds of React.")}M.Children={map:Wr,forEach:function(e,t,n){Wr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Wr(e,function(){t++}),t},toArray:function(e){return Wr(e,function(t){return t})||[]},only:function(e){if(!_s(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};M.Component=zn;M.Fragment=ff;M.Profiler=hf;M.PureComponent=ms;M.StrictMode=mf;M.Suspense=vf;M.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Pf;M.act=fc;M.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=sc({},e.props),o=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=gs.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(a in t)cc.call(t,a)&&!dc.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&s!==void 0?s[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){s=Array(a);for(var u=0;u<a;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:jr,type:e.type,key:o,ref:i,props:r,_owner:l}};M.createContext=function(e){return e={$$typeof:_f,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:gf,_context:e},e.Consumer=e};M.createElement=pc;M.createFactory=function(e){var t=pc.bind(null,e);return t.type=e,t};M.createRef=function(){return{current:null}};M.forwardRef=function(e){return{$$typeof:yf,render:e}};M.isValidElement=_s;M.lazy=function(e){return{$$typeof:Sf,_payload:{_status:-1,_result:e},_init:Cf}};M.memo=function(e,t){return{$$typeof:Ef,type:e,compare:t===void 0?null:t}};M.startTransition=function(e){var t=ho.transition;ho.transition={};try{e()}finally{ho.transition=t}};M.unstable_act=fc;M.useCallback=function(e,t){return Se.current.useCallback(e,t)};M.useContext=function(e){return Se.current.useContext(e)};M.useDebugValue=function(){};M.useDeferredValue=function(e){return Se.current.useDeferredValue(e)};M.useEffect=function(e,t){return Se.current.useEffect(e,t)};M.useId=function(){return Se.current.useId()};M.useImperativeHandle=function(e,t,n){return Se.current.useImperativeHandle(e,t,n)};M.useInsertionEffect=function(e,t){return Se.current.useInsertionEffect(e,t)};M.useLayoutEffect=function(e,t){return Se.current.useLayoutEffect(e,t)};M.useMemo=function(e,t){return Se.current.useMemo(e,t)};M.useReducer=function(e,t,n){return Se.current.useReducer(e,t,n)};M.useRef=function(e){return Se.current.useRef(e)};M.useState=function(e){return Se.current.useState(e)};M.useSyncExternalStore=function(e,t,n){return Se.current.useSyncExternalStore(e,t,n)};M.useTransition=function(){return Se.current.useTransition()};M.version="18.3.1";ic.exports=M;var N=ic.exports;const ys=rc(N);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rf=N,Lf=Symbol.for("react.element"),Tf=Symbol.for("react.fragment"),If=Object.prototype.hasOwnProperty,Af=Rf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Nf={key:!0,ref:!0,__self:!0,__source:!0};function mc(e,t,n){var r,o={},i=null,l=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)If.call(t,r)&&!Nf.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Lf,type:e,key:i,ref:l,props:o,_owner:Af.current}}ai.Fragment=Tf;ai.jsx=mc;ai.jsxs=mc;oc.exports=ai;var y=oc.exports,dl={},hc={exports:{}},Oe={},gc={exports:{}},_c={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(I,O){var j=I.length;I.push(O);e:for(;0<j;){var Y=j-1>>>1,oe=I[Y];if(0<o(oe,O))I[Y]=O,I[j]=oe,j=Y;else break e}}function n(I){return I.length===0?null:I[0]}function r(I){if(I.length===0)return null;var O=I[0],j=I.pop();if(j!==O){I[0]=j;e:for(var Y=0,oe=I.length,Gr=oe>>>1;Y<Gr;){var Ft=2*(Y+1)-1,Ti=I[Ft],Gt=Ft+1,Hr=I[Gt];if(0>o(Ti,j))Gt<oe&&0>o(Hr,Ti)?(I[Y]=Hr,I[Gt]=j,Y=Gt):(I[Y]=Ti,I[Ft]=j,Y=Ft);else if(Gt<oe&&0>o(Hr,j))I[Y]=Hr,I[Gt]=j,Y=Gt;else break e}}return O}function o(I,O){var j=I.sortIndex-O.sortIndex;return j!==0?j:I.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var a=[],u=[],d=1,f=null,m=3,g=!1,v=!1,E=!1,w=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function _(I){for(var O=n(u);O!==null;){if(O.callback===null)r(u);else if(O.startTime<=I)r(u),O.sortIndex=O.expirationTime,t(a,O);else break;O=n(u)}}function S(I){if(E=!1,_(I),!v)if(n(a)!==null)v=!0,Ut(C);else{var O=n(u);O!==null&&Li(S,O.startTime-I)}}function C(I,O){v=!1,E&&(E=!1,h(A),A=-1),g=!0;var j=m;try{for(_(O),f=n(a);f!==null&&(!(f.expirationTime>O)||I&&!_e());){var Y=f.callback;if(typeof Y=="function"){f.callback=null,m=f.priorityLevel;var oe=Y(f.expirationTime<=O);O=e.unstable_now(),typeof oe=="function"?f.callback=oe:f===n(a)&&r(a),_(O)}else r(a);f=n(a)}if(f!==null)var Gr=!0;else{var Ft=n(u);Ft!==null&&Li(S,Ft.startTime-O),Gr=!1}return Gr}finally{f=null,m=j,g=!1}}var L=!1,R=null,A=-1,B=5,b=-1;function _e(){return!(e.unstable_now()-b<B)}function vt(){if(R!==null){var I=e.unstable_now();b=I;var O=!0;try{O=R(!0,I)}finally{O?Et():(L=!1,R=null)}}else L=!1}var Et;if(typeof c=="function")Et=function(){c(vt)};else if(typeof MessageChannel<"u"){var je=new MessageChannel,St=je.port2;je.port1.onmessage=vt,Et=function(){St.postMessage(null)}}else Et=function(){w(vt,0)};function Ut(I){R=I,L||(L=!0,Et())}function Li(I,O){A=w(function(){I(e.unstable_now())},O)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(I){I.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,Ut(C))},e.unstable_forceFrameRate=function(I){0>I||125<I?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):B=0<I?Math.floor(1e3/I):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(I){switch(m){case 1:case 2:case 3:var O=3;break;default:O=m}var j=m;m=O;try{return I()}finally{m=j}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(I,O){switch(I){case 1:case 2:case 3:case 4:case 5:break;default:I=3}var j=m;m=I;try{return O()}finally{m=j}},e.unstable_scheduleCallback=function(I,O,j){var Y=e.unstable_now();switch(typeof j=="object"&&j!==null?(j=j.delay,j=typeof j=="number"&&0<j?Y+j:Y):j=Y,I){case 1:var oe=-1;break;case 2:oe=250;break;case 5:oe=**********;break;case 4:oe=1e4;break;default:oe=5e3}return oe=j+oe,I={id:d++,callback:O,priorityLevel:I,startTime:j,expirationTime:oe,sortIndex:-1},j>Y?(I.sortIndex=j,t(u,I),n(a)===null&&I===n(u)&&(E?(h(A),A=-1):E=!0,Li(S,j-Y))):(I.sortIndex=oe,t(a,I),v||g||(v=!0,Ut(C))),I},e.unstable_shouldYield=_e,e.unstable_wrapCallback=function(I){var O=m;return function(){var j=m;m=O;try{return I.apply(this,arguments)}finally{m=j}}}})(_c);gc.exports=_c;var bf=gc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Of=N,be=bf;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var yc=new Set,mr={};function ln(e,t){In(e,t),In(e+"Capture",t)}function In(e,t){for(mr[e]=t,e=0;e<t.length;e++)yc.add(t[e])}var mt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),pl=Object.prototype.hasOwnProperty,Df=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,xa={},ka={};function jf(e){return pl.call(ka,e)?!0:pl.call(xa,e)?!1:Df.test(e)?ka[e]=!0:(xa[e]=!0,!1)}function Mf(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Vf(e,t,n,r){if(t===null||typeof t>"u"||Mf(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function we(e,t,n,r,o,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var de={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){de[e]=new we(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];de[t]=new we(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){de[e]=new we(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){de[e]=new we(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){de[e]=new we(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){de[e]=new we(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){de[e]=new we(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){de[e]=new we(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){de[e]=new we(e,5,!1,e.toLowerCase(),null,!1,!1)});var vs=/[\-:]([a-z])/g;function Es(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(vs,Es);de[t]=new we(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(vs,Es);de[t]=new we(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(vs,Es);de[t]=new we(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){de[e]=new we(e,1,!1,e.toLowerCase(),null,!1,!1)});de.xlinkHref=new we("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){de[e]=new we(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ss(e,t,n,r){var o=de.hasOwnProperty(t)?de[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Vf(t,n,o,r)&&(n=null),r||o===null?jf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var yt=Of.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Kr=Symbol.for("react.element"),cn=Symbol.for("react.portal"),dn=Symbol.for("react.fragment"),ws=Symbol.for("react.strict_mode"),fl=Symbol.for("react.profiler"),vc=Symbol.for("react.provider"),Ec=Symbol.for("react.context"),xs=Symbol.for("react.forward_ref"),ml=Symbol.for("react.suspense"),hl=Symbol.for("react.suspense_list"),ks=Symbol.for("react.memo"),xt=Symbol.for("react.lazy"),Sc=Symbol.for("react.offscreen"),Ca=Symbol.iterator;function Gn(e){return e===null||typeof e!="object"?null:(e=Ca&&e[Ca]||e["@@iterator"],typeof e=="function"?e:null)}var Q=Object.assign,Ai;function Zn(e){if(Ai===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ai=t&&t[1]||""}return`
`+Ai+e}var Ni=!1;function bi(e,t){if(!e||Ni)return"";Ni=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),l=o.length-1,s=i.length-1;1<=l&&0<=s&&o[l]!==i[s];)s--;for(;1<=l&&0<=s;l--,s--)if(o[l]!==i[s]){if(l!==1||s!==1)do if(l--,s--,0>s||o[l]!==i[s]){var a=`
`+o[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=s);break}}}finally{Ni=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Zn(e):""}function zf(e){switch(e.tag){case 5:return Zn(e.type);case 16:return Zn("Lazy");case 13:return Zn("Suspense");case 19:return Zn("SuspenseList");case 0:case 2:case 15:return e=bi(e.type,!1),e;case 11:return e=bi(e.type.render,!1),e;case 1:return e=bi(e.type,!0),e;default:return""}}function gl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case dn:return"Fragment";case cn:return"Portal";case fl:return"Profiler";case ws:return"StrictMode";case ml:return"Suspense";case hl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ec:return(e.displayName||"Context")+".Consumer";case vc:return(e._context.displayName||"Context")+".Provider";case xs:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ks:return t=e.displayName||null,t!==null?t:gl(e.type)||"Memo";case xt:t=e._payload,e=e._init;try{return gl(e(t))}catch{}}return null}function Bf(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return gl(t);case 8:return t===ws?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Mt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function wc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function $f(e){var t=wc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(l){r=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Qr(e){e._valueTracker||(e._valueTracker=$f(e))}function xc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=wc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Lo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function _l(e,t){var n=t.checked;return Q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Pa(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Mt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function kc(e,t){t=t.checked,t!=null&&Ss(e,"checked",t,!1)}function yl(e,t){kc(e,t);var n=Mt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?vl(e,t.type,n):t.hasOwnProperty("defaultValue")&&vl(e,t.type,Mt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ra(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function vl(e,t,n){(t!=="number"||Lo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var er=Array.isArray;function wn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Mt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function El(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return Q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function La(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(er(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Mt(n)}}function Cc(e,t){var n=Mt(t.value),r=Mt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ta(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Pc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Sl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Pc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var qr,Rc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(qr=qr||document.createElement("div"),qr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=qr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function hr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var or={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Uf=["Webkit","ms","Moz","O"];Object.keys(or).forEach(function(e){Uf.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),or[t]=or[e]})});function Lc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||or.hasOwnProperty(e)&&or[e]?(""+t).trim():t+"px"}function Tc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Lc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Ff=Q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function wl(e,t){if(t){if(Ff[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function xl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var kl=null;function Cs(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Cl=null,xn=null,kn=null;function Ia(e){if(e=zr(e)){if(typeof Cl!="function")throw Error(k(280));var t=e.stateNode;t&&(t=fi(t),Cl(e.stateNode,e.type,t))}}function Ic(e){xn?kn?kn.push(e):kn=[e]:xn=e}function Ac(){if(xn){var e=xn,t=kn;if(kn=xn=null,Ia(e),t)for(e=0;e<t.length;e++)Ia(t[e])}}function Nc(e,t){return e(t)}function bc(){}var Oi=!1;function Oc(e,t,n){if(Oi)return e(t,n);Oi=!0;try{return Nc(e,t,n)}finally{Oi=!1,(xn!==null||kn!==null)&&(bc(),Ac())}}function gr(e,t){var n=e.stateNode;if(n===null)return null;var r=fi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var Pl=!1;if(mt)try{var Hn={};Object.defineProperty(Hn,"passive",{get:function(){Pl=!0}}),window.addEventListener("test",Hn,Hn),window.removeEventListener("test",Hn,Hn)}catch{Pl=!1}function Gf(e,t,n,r,o,i,l,s,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var ir=!1,To=null,Io=!1,Rl=null,Hf={onError:function(e){ir=!0,To=e}};function Wf(e,t,n,r,o,i,l,s,a){ir=!1,To=null,Gf.apply(Hf,arguments)}function Kf(e,t,n,r,o,i,l,s,a){if(Wf.apply(this,arguments),ir){if(ir){var u=To;ir=!1,To=null}else throw Error(k(198));Io||(Io=!0,Rl=u)}}function sn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Dc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Aa(e){if(sn(e)!==e)throw Error(k(188))}function Qf(e){var t=e.alternate;if(!t){if(t=sn(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Aa(o),e;if(i===r)return Aa(o),t;i=i.sibling}throw Error(k(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function jc(e){return e=Qf(e),e!==null?Mc(e):null}function Mc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Mc(e);if(t!==null)return t;e=e.sibling}return null}var Vc=be.unstable_scheduleCallback,Na=be.unstable_cancelCallback,qf=be.unstable_shouldYield,Yf=be.unstable_requestPaint,X=be.unstable_now,Xf=be.unstable_getCurrentPriorityLevel,Ps=be.unstable_ImmediatePriority,zc=be.unstable_UserBlockingPriority,Ao=be.unstable_NormalPriority,Jf=be.unstable_LowPriority,Bc=be.unstable_IdlePriority,ui=null,rt=null;function Zf(e){if(rt&&typeof rt.onCommitFiberRoot=="function")try{rt.onCommitFiberRoot(ui,e,void 0,(e.current.flags&128)===128)}catch{}}var qe=Math.clz32?Math.clz32:nm,em=Math.log,tm=Math.LN2;function nm(e){return e>>>=0,e===0?32:31-(em(e)/tm|0)|0}var Yr=64,Xr=4194304;function tr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function No(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,l=n&268435455;if(l!==0){var s=l&~o;s!==0?r=tr(s):(i&=l,i!==0&&(r=tr(i)))}else l=n&~o,l!==0?r=tr(l):i!==0&&(r=tr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-qe(t),o=1<<n,r|=e[n],t&=~o;return r}function rm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function om(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-qe(i),s=1<<l,a=o[l];a===-1?(!(s&n)||s&r)&&(o[l]=rm(s,t)):a<=t&&(e.expiredLanes|=s),i&=~s}}function Ll(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function $c(){var e=Yr;return Yr<<=1,!(Yr&4194240)&&(Yr=64),e}function Di(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Mr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-qe(t),e[t]=n}function im(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-qe(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Rs(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-qe(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var z=0;function Uc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Fc,Ls,Gc,Hc,Wc,Tl=!1,Jr=[],Tt=null,It=null,At=null,_r=new Map,yr=new Map,Ct=[],lm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ba(e,t){switch(e){case"focusin":case"focusout":Tt=null;break;case"dragenter":case"dragleave":It=null;break;case"mouseover":case"mouseout":At=null;break;case"pointerover":case"pointerout":_r.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":yr.delete(t.pointerId)}}function Wn(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=zr(t),t!==null&&Ls(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function sm(e,t,n,r,o){switch(t){case"focusin":return Tt=Wn(Tt,e,t,n,r,o),!0;case"dragenter":return It=Wn(It,e,t,n,r,o),!0;case"mouseover":return At=Wn(At,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return _r.set(i,Wn(_r.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,yr.set(i,Wn(yr.get(i)||null,e,t,n,r,o)),!0}return!1}function Kc(e){var t=Kt(e.target);if(t!==null){var n=sn(t);if(n!==null){if(t=n.tag,t===13){if(t=Dc(n),t!==null){e.blockedOn=t,Wc(e.priority,function(){Gc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function go(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Il(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);kl=r,n.target.dispatchEvent(r),kl=null}else return t=zr(n),t!==null&&Ls(t),e.blockedOn=n,!1;t.shift()}return!0}function Oa(e,t,n){go(e)&&n.delete(t)}function am(){Tl=!1,Tt!==null&&go(Tt)&&(Tt=null),It!==null&&go(It)&&(It=null),At!==null&&go(At)&&(At=null),_r.forEach(Oa),yr.forEach(Oa)}function Kn(e,t){e.blockedOn===t&&(e.blockedOn=null,Tl||(Tl=!0,be.unstable_scheduleCallback(be.unstable_NormalPriority,am)))}function vr(e){function t(o){return Kn(o,e)}if(0<Jr.length){Kn(Jr[0],e);for(var n=1;n<Jr.length;n++){var r=Jr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Tt!==null&&Kn(Tt,e),It!==null&&Kn(It,e),At!==null&&Kn(At,e),_r.forEach(t),yr.forEach(t),n=0;n<Ct.length;n++)r=Ct[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ct.length&&(n=Ct[0],n.blockedOn===null);)Kc(n),n.blockedOn===null&&Ct.shift()}var Cn=yt.ReactCurrentBatchConfig,bo=!0;function um(e,t,n,r){var o=z,i=Cn.transition;Cn.transition=null;try{z=1,Ts(e,t,n,r)}finally{z=o,Cn.transition=i}}function cm(e,t,n,r){var o=z,i=Cn.transition;Cn.transition=null;try{z=4,Ts(e,t,n,r)}finally{z=o,Cn.transition=i}}function Ts(e,t,n,r){if(bo){var o=Il(e,t,n,r);if(o===null)Hi(e,t,r,Oo,n),ba(e,r);else if(sm(o,e,t,n,r))r.stopPropagation();else if(ba(e,r),t&4&&-1<lm.indexOf(e)){for(;o!==null;){var i=zr(o);if(i!==null&&Fc(i),i=Il(e,t,n,r),i===null&&Hi(e,t,r,Oo,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Hi(e,t,r,null,n)}}var Oo=null;function Il(e,t,n,r){if(Oo=null,e=Cs(r),e=Kt(e),e!==null)if(t=sn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Dc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Oo=e,null}function Qc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Xf()){case Ps:return 1;case zc:return 4;case Ao:case Jf:return 16;case Bc:return 536870912;default:return 16}default:return 16}}var Rt=null,Is=null,_o=null;function qc(){if(_o)return _o;var e,t=Is,n=t.length,r,o="value"in Rt?Rt.value:Rt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===o[i-r];r++);return _o=o.slice(e,1<r?1-r:void 0)}function yo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Zr(){return!0}function Da(){return!1}function De(e){function t(n,r,o,i,l){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Zr:Da,this.isPropagationStopped=Da,this}return Q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Zr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Zr)},persist:function(){},isPersistent:Zr}),t}var Bn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},As=De(Bn),Vr=Q({},Bn,{view:0,detail:0}),dm=De(Vr),ji,Mi,Qn,ci=Q({},Vr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ns,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Qn&&(Qn&&e.type==="mousemove"?(ji=e.screenX-Qn.screenX,Mi=e.screenY-Qn.screenY):Mi=ji=0,Qn=e),ji)},movementY:function(e){return"movementY"in e?e.movementY:Mi}}),ja=De(ci),pm=Q({},ci,{dataTransfer:0}),fm=De(pm),mm=Q({},Vr,{relatedTarget:0}),Vi=De(mm),hm=Q({},Bn,{animationName:0,elapsedTime:0,pseudoElement:0}),gm=De(hm),_m=Q({},Bn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ym=De(_m),vm=Q({},Bn,{data:0}),Ma=De(vm),Em={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},wm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function xm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=wm[e])?!!t[e]:!1}function Ns(){return xm}var km=Q({},Vr,{key:function(e){if(e.key){var t=Em[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=yo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Sm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ns,charCode:function(e){return e.type==="keypress"?yo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?yo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Cm=De(km),Pm=Q({},ci,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Va=De(Pm),Rm=Q({},Vr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ns}),Lm=De(Rm),Tm=Q({},Bn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Im=De(Tm),Am=Q({},ci,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Nm=De(Am),bm=[9,13,27,32],bs=mt&&"CompositionEvent"in window,lr=null;mt&&"documentMode"in document&&(lr=document.documentMode);var Om=mt&&"TextEvent"in window&&!lr,Yc=mt&&(!bs||lr&&8<lr&&11>=lr),za=" ",Ba=!1;function Xc(e,t){switch(e){case"keyup":return bm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Jc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var pn=!1;function Dm(e,t){switch(e){case"compositionend":return Jc(t);case"keypress":return t.which!==32?null:(Ba=!0,za);case"textInput":return e=t.data,e===za&&Ba?null:e;default:return null}}function jm(e,t){if(pn)return e==="compositionend"||!bs&&Xc(e,t)?(e=qc(),_o=Is=Rt=null,pn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Yc&&t.locale!=="ko"?null:t.data;default:return null}}var Mm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function $a(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Mm[e.type]:t==="textarea"}function Zc(e,t,n,r){Ic(r),t=Do(t,"onChange"),0<t.length&&(n=new As("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var sr=null,Er=null;function Vm(e){cd(e,0)}function di(e){var t=hn(e);if(xc(t))return e}function zm(e,t){if(e==="change")return t}var ed=!1;if(mt){var zi;if(mt){var Bi="oninput"in document;if(!Bi){var Ua=document.createElement("div");Ua.setAttribute("oninput","return;"),Bi=typeof Ua.oninput=="function"}zi=Bi}else zi=!1;ed=zi&&(!document.documentMode||9<document.documentMode)}function Fa(){sr&&(sr.detachEvent("onpropertychange",td),Er=sr=null)}function td(e){if(e.propertyName==="value"&&di(Er)){var t=[];Zc(t,Er,e,Cs(e)),Oc(Vm,t)}}function Bm(e,t,n){e==="focusin"?(Fa(),sr=t,Er=n,sr.attachEvent("onpropertychange",td)):e==="focusout"&&Fa()}function $m(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return di(Er)}function Um(e,t){if(e==="click")return di(t)}function Fm(e,t){if(e==="input"||e==="change")return di(t)}function Gm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Xe=typeof Object.is=="function"?Object.is:Gm;function Sr(e,t){if(Xe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!pl.call(t,o)||!Xe(e[o],t[o]))return!1}return!0}function Ga(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ha(e,t){var n=Ga(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ga(n)}}function nd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?nd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function rd(){for(var e=window,t=Lo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Lo(e.document)}return t}function Os(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Hm(e){var t=rd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&nd(n.ownerDocument.documentElement,n)){if(r!==null&&Os(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Ha(n,i);var l=Ha(n,r);o&&l&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Wm=mt&&"documentMode"in document&&11>=document.documentMode,fn=null,Al=null,ar=null,Nl=!1;function Wa(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Nl||fn==null||fn!==Lo(r)||(r=fn,"selectionStart"in r&&Os(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ar&&Sr(ar,r)||(ar=r,r=Do(Al,"onSelect"),0<r.length&&(t=new As("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=fn)))}function eo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var mn={animationend:eo("Animation","AnimationEnd"),animationiteration:eo("Animation","AnimationIteration"),animationstart:eo("Animation","AnimationStart"),transitionend:eo("Transition","TransitionEnd")},$i={},od={};mt&&(od=document.createElement("div").style,"AnimationEvent"in window||(delete mn.animationend.animation,delete mn.animationiteration.animation,delete mn.animationstart.animation),"TransitionEvent"in window||delete mn.transitionend.transition);function pi(e){if($i[e])return $i[e];if(!mn[e])return e;var t=mn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in od)return $i[e]=t[n];return e}var id=pi("animationend"),ld=pi("animationiteration"),sd=pi("animationstart"),ad=pi("transitionend"),ud=new Map,Ka="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function zt(e,t){ud.set(e,t),ln(t,[e])}for(var Ui=0;Ui<Ka.length;Ui++){var Fi=Ka[Ui],Km=Fi.toLowerCase(),Qm=Fi[0].toUpperCase()+Fi.slice(1);zt(Km,"on"+Qm)}zt(id,"onAnimationEnd");zt(ld,"onAnimationIteration");zt(sd,"onAnimationStart");zt("dblclick","onDoubleClick");zt("focusin","onFocus");zt("focusout","onBlur");zt(ad,"onTransitionEnd");In("onMouseEnter",["mouseout","mouseover"]);In("onMouseLeave",["mouseout","mouseover"]);In("onPointerEnter",["pointerout","pointerover"]);In("onPointerLeave",["pointerout","pointerover"]);ln("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ln("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ln("onBeforeInput",["compositionend","keypress","textInput","paste"]);ln("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ln("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ln("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var nr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),qm=new Set("cancel close invalid load scroll toggle".split(" ").concat(nr));function Qa(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Kf(r,t,void 0,e),e.currentTarget=null}function cd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var s=r[l],a=s.instance,u=s.currentTarget;if(s=s.listener,a!==i&&o.isPropagationStopped())break e;Qa(o,s,u),i=a}else for(l=0;l<r.length;l++){if(s=r[l],a=s.instance,u=s.currentTarget,s=s.listener,a!==i&&o.isPropagationStopped())break e;Qa(o,s,u),i=a}}}if(Io)throw e=Rl,Io=!1,Rl=null,e}function F(e,t){var n=t[Ml];n===void 0&&(n=t[Ml]=new Set);var r=e+"__bubble";n.has(r)||(dd(t,e,2,!1),n.add(r))}function Gi(e,t,n){var r=0;t&&(r|=4),dd(n,e,r,t)}var to="_reactListening"+Math.random().toString(36).slice(2);function wr(e){if(!e[to]){e[to]=!0,yc.forEach(function(n){n!=="selectionchange"&&(qm.has(n)||Gi(n,!1,e),Gi(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[to]||(t[to]=!0,Gi("selectionchange",!1,t))}}function dd(e,t,n,r){switch(Qc(t)){case 1:var o=um;break;case 4:o=cm;break;default:o=Ts}n=o.bind(null,t,n,e),o=void 0,!Pl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Hi(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var s=r.stateNode.containerInfo;if(s===o||s.nodeType===8&&s.parentNode===o)break;if(l===4)for(l=r.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;l=l.return}for(;s!==null;){if(l=Kt(s),l===null)return;if(a=l.tag,a===5||a===6){r=i=l;continue e}s=s.parentNode}}r=r.return}Oc(function(){var u=i,d=Cs(n),f=[];e:{var m=ud.get(e);if(m!==void 0){var g=As,v=e;switch(e){case"keypress":if(yo(n)===0)break e;case"keydown":case"keyup":g=Cm;break;case"focusin":v="focus",g=Vi;break;case"focusout":v="blur",g=Vi;break;case"beforeblur":case"afterblur":g=Vi;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=ja;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=fm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=Lm;break;case id:case ld:case sd:g=gm;break;case ad:g=Im;break;case"scroll":g=dm;break;case"wheel":g=Nm;break;case"copy":case"cut":case"paste":g=ym;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Va}var E=(t&4)!==0,w=!E&&e==="scroll",h=E?m!==null?m+"Capture":null:m;E=[];for(var c=u,_;c!==null;){_=c;var S=_.stateNode;if(_.tag===5&&S!==null&&(_=S,h!==null&&(S=gr(c,h),S!=null&&E.push(xr(c,S,_)))),w)break;c=c.return}0<E.length&&(m=new g(m,v,null,n,d),f.push({event:m,listeners:E}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",m&&n!==kl&&(v=n.relatedTarget||n.fromElement)&&(Kt(v)||v[ht]))break e;if((g||m)&&(m=d.window===d?d:(m=d.ownerDocument)?m.defaultView||m.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=u,v=v?Kt(v):null,v!==null&&(w=sn(v),v!==w||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=u),g!==v)){if(E=ja,S="onMouseLeave",h="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(E=Va,S="onPointerLeave",h="onPointerEnter",c="pointer"),w=g==null?m:hn(g),_=v==null?m:hn(v),m=new E(S,c+"leave",g,n,d),m.target=w,m.relatedTarget=_,S=null,Kt(d)===u&&(E=new E(h,c+"enter",v,n,d),E.target=_,E.relatedTarget=w,S=E),w=S,g&&v)t:{for(E=g,h=v,c=0,_=E;_;_=un(_))c++;for(_=0,S=h;S;S=un(S))_++;for(;0<c-_;)E=un(E),c--;for(;0<_-c;)h=un(h),_--;for(;c--;){if(E===h||h!==null&&E===h.alternate)break t;E=un(E),h=un(h)}E=null}else E=null;g!==null&&qa(f,m,g,E,!1),v!==null&&w!==null&&qa(f,w,v,E,!0)}}e:{if(m=u?hn(u):window,g=m.nodeName&&m.nodeName.toLowerCase(),g==="select"||g==="input"&&m.type==="file")var C=zm;else if($a(m))if(ed)C=Fm;else{C=$m;var L=Bm}else(g=m.nodeName)&&g.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(C=Um);if(C&&(C=C(e,u))){Zc(f,C,n,d);break e}L&&L(e,m,u),e==="focusout"&&(L=m._wrapperState)&&L.controlled&&m.type==="number"&&vl(m,"number",m.value)}switch(L=u?hn(u):window,e){case"focusin":($a(L)||L.contentEditable==="true")&&(fn=L,Al=u,ar=null);break;case"focusout":ar=Al=fn=null;break;case"mousedown":Nl=!0;break;case"contextmenu":case"mouseup":case"dragend":Nl=!1,Wa(f,n,d);break;case"selectionchange":if(Wm)break;case"keydown":case"keyup":Wa(f,n,d)}var R;if(bs)e:{switch(e){case"compositionstart":var A="onCompositionStart";break e;case"compositionend":A="onCompositionEnd";break e;case"compositionupdate":A="onCompositionUpdate";break e}A=void 0}else pn?Xc(e,n)&&(A="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(A="onCompositionStart");A&&(Yc&&n.locale!=="ko"&&(pn||A!=="onCompositionStart"?A==="onCompositionEnd"&&pn&&(R=qc()):(Rt=d,Is="value"in Rt?Rt.value:Rt.textContent,pn=!0)),L=Do(u,A),0<L.length&&(A=new Ma(A,e,null,n,d),f.push({event:A,listeners:L}),R?A.data=R:(R=Jc(n),R!==null&&(A.data=R)))),(R=Om?Dm(e,n):jm(e,n))&&(u=Do(u,"onBeforeInput"),0<u.length&&(d=new Ma("onBeforeInput","beforeinput",null,n,d),f.push({event:d,listeners:u}),d.data=R))}cd(f,t)})}function xr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Do(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=gr(e,n),i!=null&&r.unshift(xr(e,i,o)),i=gr(e,t),i!=null&&r.push(xr(e,i,o))),e=e.return}return r}function un(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function qa(e,t,n,r,o){for(var i=t._reactName,l=[];n!==null&&n!==r;){var s=n,a=s.alternate,u=s.stateNode;if(a!==null&&a===r)break;s.tag===5&&u!==null&&(s=u,o?(a=gr(n,i),a!=null&&l.unshift(xr(n,a,s))):o||(a=gr(n,i),a!=null&&l.push(xr(n,a,s)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var Ym=/\r\n?/g,Xm=/\u0000|\uFFFD/g;function Ya(e){return(typeof e=="string"?e:""+e).replace(Ym,`
`).replace(Xm,"")}function no(e,t,n){if(t=Ya(t),Ya(e)!==t&&n)throw Error(k(425))}function jo(){}var bl=null,Ol=null;function Dl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var jl=typeof setTimeout=="function"?setTimeout:void 0,Jm=typeof clearTimeout=="function"?clearTimeout:void 0,Xa=typeof Promise=="function"?Promise:void 0,Zm=typeof queueMicrotask=="function"?queueMicrotask:typeof Xa<"u"?function(e){return Xa.resolve(null).then(e).catch(eh)}:jl;function eh(e){setTimeout(function(){throw e})}function Wi(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),vr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);vr(t)}function Nt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ja(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var $n=Math.random().toString(36).slice(2),nt="__reactFiber$"+$n,kr="__reactProps$"+$n,ht="__reactContainer$"+$n,Ml="__reactEvents$"+$n,th="__reactListeners$"+$n,nh="__reactHandles$"+$n;function Kt(e){var t=e[nt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ht]||n[nt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ja(e);e!==null;){if(n=e[nt])return n;e=Ja(e)}return t}e=n,n=e.parentNode}return null}function zr(e){return e=e[nt]||e[ht],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function hn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function fi(e){return e[kr]||null}var Vl=[],gn=-1;function Bt(e){return{current:e}}function G(e){0>gn||(e.current=Vl[gn],Vl[gn]=null,gn--)}function U(e,t){gn++,Vl[gn]=e.current,e.current=t}var Vt={},ge=Bt(Vt),Ce=Bt(!1),en=Vt;function An(e,t){var n=e.type.contextTypes;if(!n)return Vt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Pe(e){return e=e.childContextTypes,e!=null}function Mo(){G(Ce),G(ge)}function Za(e,t,n){if(ge.current!==Vt)throw Error(k(168));U(ge,t),U(Ce,n)}function pd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(k(108,Bf(e)||"Unknown",o));return Q({},n,r)}function Vo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Vt,en=ge.current,U(ge,e),U(Ce,Ce.current),!0}function eu(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=pd(e,t,en),r.__reactInternalMemoizedMergedChildContext=e,G(Ce),G(ge),U(ge,e)):G(Ce),U(Ce,n)}var st=null,mi=!1,Ki=!1;function fd(e){st===null?st=[e]:st.push(e)}function rh(e){mi=!0,fd(e)}function $t(){if(!Ki&&st!==null){Ki=!0;var e=0,t=z;try{var n=st;for(z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}st=null,mi=!1}catch(o){throw st!==null&&(st=st.slice(e+1)),Vc(Ps,$t),o}finally{z=t,Ki=!1}}return null}var _n=[],yn=0,zo=null,Bo=0,Ve=[],ze=0,tn=null,ut=1,ct="";function Ht(e,t){_n[yn++]=Bo,_n[yn++]=zo,zo=e,Bo=t}function md(e,t,n){Ve[ze++]=ut,Ve[ze++]=ct,Ve[ze++]=tn,tn=e;var r=ut;e=ct;var o=32-qe(r)-1;r&=~(1<<o),n+=1;var i=32-qe(t)+o;if(30<i){var l=o-o%5;i=(r&(1<<l)-1).toString(32),r>>=l,o-=l,ut=1<<32-qe(t)+o|n<<o|r,ct=i+e}else ut=1<<i|n<<o|r,ct=e}function Ds(e){e.return!==null&&(Ht(e,1),md(e,1,0))}function js(e){for(;e===zo;)zo=_n[--yn],_n[yn]=null,Bo=_n[--yn],_n[yn]=null;for(;e===tn;)tn=Ve[--ze],Ve[ze]=null,ct=Ve[--ze],Ve[ze]=null,ut=Ve[--ze],Ve[ze]=null}var Ne=null,Ae=null,H=!1,Qe=null;function hd(e,t){var n=$e(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function tu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ne=e,Ae=Nt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ne=e,Ae=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=tn!==null?{id:ut,overflow:ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=$e(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ne=e,Ae=null,!0):!1;default:return!1}}function zl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Bl(e){if(H){var t=Ae;if(t){var n=t;if(!tu(e,t)){if(zl(e))throw Error(k(418));t=Nt(n.nextSibling);var r=Ne;t&&tu(e,t)?hd(r,n):(e.flags=e.flags&-4097|2,H=!1,Ne=e)}}else{if(zl(e))throw Error(k(418));e.flags=e.flags&-4097|2,H=!1,Ne=e}}}function nu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ne=e}function ro(e){if(e!==Ne)return!1;if(!H)return nu(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Dl(e.type,e.memoizedProps)),t&&(t=Ae)){if(zl(e))throw gd(),Error(k(418));for(;t;)hd(e,t),t=Nt(t.nextSibling)}if(nu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ae=Nt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ae=null}}else Ae=Ne?Nt(e.stateNode.nextSibling):null;return!0}function gd(){for(var e=Ae;e;)e=Nt(e.nextSibling)}function Nn(){Ae=Ne=null,H=!1}function Ms(e){Qe===null?Qe=[e]:Qe.push(e)}var oh=yt.ReactCurrentBatchConfig;function qn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var s=o.refs;l===null?delete s[i]:s[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function oo(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ru(e){var t=e._init;return t(e._payload)}function _d(e){function t(h,c){if(e){var _=h.deletions;_===null?(h.deletions=[c],h.flags|=16):_.push(c)}}function n(h,c){if(!e)return null;for(;c!==null;)t(h,c),c=c.sibling;return null}function r(h,c){for(h=new Map;c!==null;)c.key!==null?h.set(c.key,c):h.set(c.index,c),c=c.sibling;return h}function o(h,c){return h=jt(h,c),h.index=0,h.sibling=null,h}function i(h,c,_){return h.index=_,e?(_=h.alternate,_!==null?(_=_.index,_<c?(h.flags|=2,c):_):(h.flags|=2,c)):(h.flags|=1048576,c)}function l(h){return e&&h.alternate===null&&(h.flags|=2),h}function s(h,c,_,S){return c===null||c.tag!==6?(c=el(_,h.mode,S),c.return=h,c):(c=o(c,_),c.return=h,c)}function a(h,c,_,S){var C=_.type;return C===dn?d(h,c,_.props.children,S,_.key):c!==null&&(c.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===xt&&ru(C)===c.type)?(S=o(c,_.props),S.ref=qn(h,c,_),S.return=h,S):(S=Co(_.type,_.key,_.props,null,h.mode,S),S.ref=qn(h,c,_),S.return=h,S)}function u(h,c,_,S){return c===null||c.tag!==4||c.stateNode.containerInfo!==_.containerInfo||c.stateNode.implementation!==_.implementation?(c=tl(_,h.mode,S),c.return=h,c):(c=o(c,_.children||[]),c.return=h,c)}function d(h,c,_,S,C){return c===null||c.tag!==7?(c=Zt(_,h.mode,S,C),c.return=h,c):(c=o(c,_),c.return=h,c)}function f(h,c,_){if(typeof c=="string"&&c!==""||typeof c=="number")return c=el(""+c,h.mode,_),c.return=h,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case Kr:return _=Co(c.type,c.key,c.props,null,h.mode,_),_.ref=qn(h,null,c),_.return=h,_;case cn:return c=tl(c,h.mode,_),c.return=h,c;case xt:var S=c._init;return f(h,S(c._payload),_)}if(er(c)||Gn(c))return c=Zt(c,h.mode,_,null),c.return=h,c;oo(h,c)}return null}function m(h,c,_,S){var C=c!==null?c.key:null;if(typeof _=="string"&&_!==""||typeof _=="number")return C!==null?null:s(h,c,""+_,S);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case Kr:return _.key===C?a(h,c,_,S):null;case cn:return _.key===C?u(h,c,_,S):null;case xt:return C=_._init,m(h,c,C(_._payload),S)}if(er(_)||Gn(_))return C!==null?null:d(h,c,_,S,null);oo(h,_)}return null}function g(h,c,_,S,C){if(typeof S=="string"&&S!==""||typeof S=="number")return h=h.get(_)||null,s(c,h,""+S,C);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Kr:return h=h.get(S.key===null?_:S.key)||null,a(c,h,S,C);case cn:return h=h.get(S.key===null?_:S.key)||null,u(c,h,S,C);case xt:var L=S._init;return g(h,c,_,L(S._payload),C)}if(er(S)||Gn(S))return h=h.get(_)||null,d(c,h,S,C,null);oo(c,S)}return null}function v(h,c,_,S){for(var C=null,L=null,R=c,A=c=0,B=null;R!==null&&A<_.length;A++){R.index>A?(B=R,R=null):B=R.sibling;var b=m(h,R,_[A],S);if(b===null){R===null&&(R=B);break}e&&R&&b.alternate===null&&t(h,R),c=i(b,c,A),L===null?C=b:L.sibling=b,L=b,R=B}if(A===_.length)return n(h,R),H&&Ht(h,A),C;if(R===null){for(;A<_.length;A++)R=f(h,_[A],S),R!==null&&(c=i(R,c,A),L===null?C=R:L.sibling=R,L=R);return H&&Ht(h,A),C}for(R=r(h,R);A<_.length;A++)B=g(R,h,A,_[A],S),B!==null&&(e&&B.alternate!==null&&R.delete(B.key===null?A:B.key),c=i(B,c,A),L===null?C=B:L.sibling=B,L=B);return e&&R.forEach(function(_e){return t(h,_e)}),H&&Ht(h,A),C}function E(h,c,_,S){var C=Gn(_);if(typeof C!="function")throw Error(k(150));if(_=C.call(_),_==null)throw Error(k(151));for(var L=C=null,R=c,A=c=0,B=null,b=_.next();R!==null&&!b.done;A++,b=_.next()){R.index>A?(B=R,R=null):B=R.sibling;var _e=m(h,R,b.value,S);if(_e===null){R===null&&(R=B);break}e&&R&&_e.alternate===null&&t(h,R),c=i(_e,c,A),L===null?C=_e:L.sibling=_e,L=_e,R=B}if(b.done)return n(h,R),H&&Ht(h,A),C;if(R===null){for(;!b.done;A++,b=_.next())b=f(h,b.value,S),b!==null&&(c=i(b,c,A),L===null?C=b:L.sibling=b,L=b);return H&&Ht(h,A),C}for(R=r(h,R);!b.done;A++,b=_.next())b=g(R,h,A,b.value,S),b!==null&&(e&&b.alternate!==null&&R.delete(b.key===null?A:b.key),c=i(b,c,A),L===null?C=b:L.sibling=b,L=b);return e&&R.forEach(function(vt){return t(h,vt)}),H&&Ht(h,A),C}function w(h,c,_,S){if(typeof _=="object"&&_!==null&&_.type===dn&&_.key===null&&(_=_.props.children),typeof _=="object"&&_!==null){switch(_.$$typeof){case Kr:e:{for(var C=_.key,L=c;L!==null;){if(L.key===C){if(C=_.type,C===dn){if(L.tag===7){n(h,L.sibling),c=o(L,_.props.children),c.return=h,h=c;break e}}else if(L.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===xt&&ru(C)===L.type){n(h,L.sibling),c=o(L,_.props),c.ref=qn(h,L,_),c.return=h,h=c;break e}n(h,L);break}else t(h,L);L=L.sibling}_.type===dn?(c=Zt(_.props.children,h.mode,S,_.key),c.return=h,h=c):(S=Co(_.type,_.key,_.props,null,h.mode,S),S.ref=qn(h,c,_),S.return=h,h=S)}return l(h);case cn:e:{for(L=_.key;c!==null;){if(c.key===L)if(c.tag===4&&c.stateNode.containerInfo===_.containerInfo&&c.stateNode.implementation===_.implementation){n(h,c.sibling),c=o(c,_.children||[]),c.return=h,h=c;break e}else{n(h,c);break}else t(h,c);c=c.sibling}c=tl(_,h.mode,S),c.return=h,h=c}return l(h);case xt:return L=_._init,w(h,c,L(_._payload),S)}if(er(_))return v(h,c,_,S);if(Gn(_))return E(h,c,_,S);oo(h,_)}return typeof _=="string"&&_!==""||typeof _=="number"?(_=""+_,c!==null&&c.tag===6?(n(h,c.sibling),c=o(c,_),c.return=h,h=c):(n(h,c),c=el(_,h.mode,S),c.return=h,h=c),l(h)):n(h,c)}return w}var bn=_d(!0),yd=_d(!1),$o=Bt(null),Uo=null,vn=null,Vs=null;function zs(){Vs=vn=Uo=null}function Bs(e){var t=$o.current;G($o),e._currentValue=t}function $l(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Pn(e,t){Uo=e,Vs=vn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ke=!0),e.firstContext=null)}function Fe(e){var t=e._currentValue;if(Vs!==e)if(e={context:e,memoizedValue:t,next:null},vn===null){if(Uo===null)throw Error(k(308));vn=e,Uo.dependencies={lanes:0,firstContext:e}}else vn=vn.next=e;return t}var Qt=null;function $s(e){Qt===null?Qt=[e]:Qt.push(e)}function vd(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,$s(t)):(n.next=o.next,o.next=n),t.interleaved=n,gt(e,r)}function gt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var kt=!1;function Us(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ed(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function pt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function bt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,V&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,gt(e,n)}return o=r.interleaved,o===null?(t.next=t,$s(r)):(t.next=o.next,o.next=t),r.interleaved=t,gt(e,n)}function vo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Rs(e,n)}}function ou(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=l:i=i.next=l,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Fo(e,t,n,r){var o=e.updateQueue;kt=!1;var i=o.firstBaseUpdate,l=o.lastBaseUpdate,s=o.shared.pending;if(s!==null){o.shared.pending=null;var a=s,u=a.next;a.next=null,l===null?i=u:l.next=u,l=a;var d=e.alternate;d!==null&&(d=d.updateQueue,s=d.lastBaseUpdate,s!==l&&(s===null?d.firstBaseUpdate=u:s.next=u,d.lastBaseUpdate=a))}if(i!==null){var f=o.baseState;l=0,d=u=a=null,s=i;do{var m=s.lane,g=s.eventTime;if((r&m)===m){d!==null&&(d=d.next={eventTime:g,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var v=e,E=s;switch(m=t,g=n,E.tag){case 1:if(v=E.payload,typeof v=="function"){f=v.call(g,f,m);break e}f=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=E.payload,m=typeof v=="function"?v.call(g,f,m):v,m==null)break e;f=Q({},f,m);break e;case 2:kt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,m=o.effects,m===null?o.effects=[s]:m.push(s))}else g={eventTime:g,lane:m,tag:s.tag,payload:s.payload,callback:s.callback,next:null},d===null?(u=d=g,a=f):d=d.next=g,l|=m;if(s=s.next,s===null){if(s=o.shared.pending,s===null)break;m=s,s=m.next,m.next=null,o.lastBaseUpdate=m,o.shared.pending=null}}while(!0);if(d===null&&(a=f),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do l|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);rn|=l,e.lanes=l,e.memoizedState=f}}function iu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(k(191,o));o.call(r)}}}var Br={},ot=Bt(Br),Cr=Bt(Br),Pr=Bt(Br);function qt(e){if(e===Br)throw Error(k(174));return e}function Fs(e,t){switch(U(Pr,t),U(Cr,e),U(ot,Br),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Sl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Sl(t,e)}G(ot),U(ot,t)}function On(){G(ot),G(Cr),G(Pr)}function Sd(e){qt(Pr.current);var t=qt(ot.current),n=Sl(t,e.type);t!==n&&(U(Cr,e),U(ot,n))}function Gs(e){Cr.current===e&&(G(ot),G(Cr))}var W=Bt(0);function Go(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Qi=[];function Hs(){for(var e=0;e<Qi.length;e++)Qi[e]._workInProgressVersionPrimary=null;Qi.length=0}var Eo=yt.ReactCurrentDispatcher,qi=yt.ReactCurrentBatchConfig,nn=0,K=null,ee=null,le=null,Ho=!1,ur=!1,Rr=0,ih=0;function pe(){throw Error(k(321))}function Ws(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Xe(e[n],t[n]))return!1;return!0}function Ks(e,t,n,r,o,i){if(nn=i,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Eo.current=e===null||e.memoizedState===null?uh:ch,e=n(r,o),ur){i=0;do{if(ur=!1,Rr=0,25<=i)throw Error(k(301));i+=1,le=ee=null,t.updateQueue=null,Eo.current=dh,e=n(r,o)}while(ur)}if(Eo.current=Wo,t=ee!==null&&ee.next!==null,nn=0,le=ee=K=null,Ho=!1,t)throw Error(k(300));return e}function Qs(){var e=Rr!==0;return Rr=0,e}function tt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return le===null?K.memoizedState=le=e:le=le.next=e,le}function Ge(){if(ee===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=ee.next;var t=le===null?K.memoizedState:le.next;if(t!==null)le=t,ee=e;else{if(e===null)throw Error(k(310));ee=e,e={memoizedState:ee.memoizedState,baseState:ee.baseState,baseQueue:ee.baseQueue,queue:ee.queue,next:null},le===null?K.memoizedState=le=e:le=le.next=e}return le}function Lr(e,t){return typeof t=="function"?t(e):t}function Yi(e){var t=Ge(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=ee,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var s=l=null,a=null,u=i;do{var d=u.lane;if((nn&d)===d)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(s=a=f,l=r):a=a.next=f,K.lanes|=d,rn|=d}u=u.next}while(u!==null&&u!==i);a===null?l=r:a.next=s,Xe(r,t.memoizedState)||(ke=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,K.lanes|=i,rn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Xi(e){var t=Ge(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var l=o=o.next;do i=e(i,l.action),l=l.next;while(l!==o);Xe(i,t.memoizedState)||(ke=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function wd(){}function xd(e,t){var n=K,r=Ge(),o=t(),i=!Xe(r.memoizedState,o);if(i&&(r.memoizedState=o,ke=!0),r=r.queue,qs(Pd.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||le!==null&&le.memoizedState.tag&1){if(n.flags|=2048,Tr(9,Cd.bind(null,n,r,o,t),void 0,null),se===null)throw Error(k(349));nn&30||kd(n,t,o)}return o}function kd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Cd(e,t,n,r){t.value=n,t.getSnapshot=r,Rd(t)&&Ld(e)}function Pd(e,t,n){return n(function(){Rd(t)&&Ld(e)})}function Rd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Xe(e,n)}catch{return!0}}function Ld(e){var t=gt(e,1);t!==null&&Ye(t,e,1,-1)}function lu(e){var t=tt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Lr,lastRenderedState:e},t.queue=e,e=e.dispatch=ah.bind(null,K,e),[t.memoizedState,e]}function Tr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Td(){return Ge().memoizedState}function So(e,t,n,r){var o=tt();K.flags|=e,o.memoizedState=Tr(1|t,n,void 0,r===void 0?null:r)}function hi(e,t,n,r){var o=Ge();r=r===void 0?null:r;var i=void 0;if(ee!==null){var l=ee.memoizedState;if(i=l.destroy,r!==null&&Ws(r,l.deps)){o.memoizedState=Tr(t,n,i,r);return}}K.flags|=e,o.memoizedState=Tr(1|t,n,i,r)}function su(e,t){return So(8390656,8,e,t)}function qs(e,t){return hi(2048,8,e,t)}function Id(e,t){return hi(4,2,e,t)}function Ad(e,t){return hi(4,4,e,t)}function Nd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function bd(e,t,n){return n=n!=null?n.concat([e]):null,hi(4,4,Nd.bind(null,t,e),n)}function Ys(){}function Od(e,t){var n=Ge();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ws(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Dd(e,t){var n=Ge();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ws(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function jd(e,t,n){return nn&21?(Xe(n,t)||(n=$c(),K.lanes|=n,rn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ke=!0),e.memoizedState=n)}function lh(e,t){var n=z;z=n!==0&&4>n?n:4,e(!0);var r=qi.transition;qi.transition={};try{e(!1),t()}finally{z=n,qi.transition=r}}function Md(){return Ge().memoizedState}function sh(e,t,n){var r=Dt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Vd(e))zd(t,n);else if(n=vd(e,t,n,r),n!==null){var o=Ee();Ye(n,e,r,o),Bd(n,t,r)}}function ah(e,t,n){var r=Dt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Vd(e))zd(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,s=i(l,n);if(o.hasEagerState=!0,o.eagerState=s,Xe(s,l)){var a=t.interleaved;a===null?(o.next=o,$s(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=vd(e,t,o,r),n!==null&&(o=Ee(),Ye(n,e,r,o),Bd(n,t,r))}}function Vd(e){var t=e.alternate;return e===K||t!==null&&t===K}function zd(e,t){ur=Ho=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Bd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Rs(e,n)}}var Wo={readContext:Fe,useCallback:pe,useContext:pe,useEffect:pe,useImperativeHandle:pe,useInsertionEffect:pe,useLayoutEffect:pe,useMemo:pe,useReducer:pe,useRef:pe,useState:pe,useDebugValue:pe,useDeferredValue:pe,useTransition:pe,useMutableSource:pe,useSyncExternalStore:pe,useId:pe,unstable_isNewReconciler:!1},uh={readContext:Fe,useCallback:function(e,t){return tt().memoizedState=[e,t===void 0?null:t],e},useContext:Fe,useEffect:su,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,So(4194308,4,Nd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return So(4194308,4,e,t)},useInsertionEffect:function(e,t){return So(4,2,e,t)},useMemo:function(e,t){var n=tt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=tt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=sh.bind(null,K,e),[r.memoizedState,e]},useRef:function(e){var t=tt();return e={current:e},t.memoizedState=e},useState:lu,useDebugValue:Ys,useDeferredValue:function(e){return tt().memoizedState=e},useTransition:function(){var e=lu(!1),t=e[0];return e=lh.bind(null,e[1]),tt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=K,o=tt();if(H){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),se===null)throw Error(k(349));nn&30||kd(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,su(Pd.bind(null,r,i,e),[e]),r.flags|=2048,Tr(9,Cd.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=tt(),t=se.identifierPrefix;if(H){var n=ct,r=ut;n=(r&~(1<<32-qe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Rr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=ih++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ch={readContext:Fe,useCallback:Od,useContext:Fe,useEffect:qs,useImperativeHandle:bd,useInsertionEffect:Id,useLayoutEffect:Ad,useMemo:Dd,useReducer:Yi,useRef:Td,useState:function(){return Yi(Lr)},useDebugValue:Ys,useDeferredValue:function(e){var t=Ge();return jd(t,ee.memoizedState,e)},useTransition:function(){var e=Yi(Lr)[0],t=Ge().memoizedState;return[e,t]},useMutableSource:wd,useSyncExternalStore:xd,useId:Md,unstable_isNewReconciler:!1},dh={readContext:Fe,useCallback:Od,useContext:Fe,useEffect:qs,useImperativeHandle:bd,useInsertionEffect:Id,useLayoutEffect:Ad,useMemo:Dd,useReducer:Xi,useRef:Td,useState:function(){return Xi(Lr)},useDebugValue:Ys,useDeferredValue:function(e){var t=Ge();return ee===null?t.memoizedState=e:jd(t,ee.memoizedState,e)},useTransition:function(){var e=Xi(Lr)[0],t=Ge().memoizedState;return[e,t]},useMutableSource:wd,useSyncExternalStore:xd,useId:Md,unstable_isNewReconciler:!1};function We(e,t){if(e&&e.defaultProps){t=Q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ul(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var gi={isMounted:function(e){return(e=e._reactInternals)?sn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ee(),o=Dt(e),i=pt(r,o);i.payload=t,n!=null&&(i.callback=n),t=bt(e,i,o),t!==null&&(Ye(t,e,o,r),vo(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ee(),o=Dt(e),i=pt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=bt(e,i,o),t!==null&&(Ye(t,e,o,r),vo(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ee(),r=Dt(e),o=pt(n,r);o.tag=2,t!=null&&(o.callback=t),t=bt(e,o,r),t!==null&&(Ye(t,e,r,n),vo(t,e,r))}};function au(e,t,n,r,o,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,l):t.prototype&&t.prototype.isPureReactComponent?!Sr(n,r)||!Sr(o,i):!0}function $d(e,t,n){var r=!1,o=Vt,i=t.contextType;return typeof i=="object"&&i!==null?i=Fe(i):(o=Pe(t)?en:ge.current,r=t.contextTypes,i=(r=r!=null)?An(e,o):Vt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=gi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function uu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&gi.enqueueReplaceState(t,t.state,null)}function Fl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Us(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Fe(i):(i=Pe(t)?en:ge.current,o.context=An(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ul(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&gi.enqueueReplaceState(o,o.state,null),Fo(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Dn(e,t){try{var n="",r=t;do n+=zf(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Ji(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Gl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var ph=typeof WeakMap=="function"?WeakMap:Map;function Ud(e,t,n){n=pt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Qo||(Qo=!0,es=r),Gl(e,t)},n}function Fd(e,t,n){n=pt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Gl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Gl(e,t),typeof r!="function"&&(Ot===null?Ot=new Set([this]):Ot.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function cu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new ph;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Ph.bind(null,e,t,n),t.then(e,e))}function du(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function pu(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=pt(-1,1),t.tag=2,bt(n,t,1))),n.lanes|=1),e)}var fh=yt.ReactCurrentOwner,ke=!1;function ye(e,t,n,r){t.child=e===null?yd(t,null,n,r):bn(t,e.child,n,r)}function fu(e,t,n,r,o){n=n.render;var i=t.ref;return Pn(t,o),r=Ks(e,t,n,r,i,o),n=Qs(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,_t(e,t,o)):(H&&n&&Ds(t),t.flags|=1,ye(e,t,r,o),t.child)}function mu(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!oa(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Gd(e,t,i,r,o)):(e=Co(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var l=i.memoizedProps;if(n=n.compare,n=n!==null?n:Sr,n(l,r)&&e.ref===t.ref)return _t(e,t,o)}return t.flags|=1,e=jt(i,r),e.ref=t.ref,e.return=t,t.child=e}function Gd(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Sr(i,r)&&e.ref===t.ref)if(ke=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(ke=!0);else return t.lanes=e.lanes,_t(e,t,o)}return Hl(e,t,n,r,o)}function Hd(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},U(Sn,Ie),Ie|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,U(Sn,Ie),Ie|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,U(Sn,Ie),Ie|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,U(Sn,Ie),Ie|=r;return ye(e,t,o,n),t.child}function Wd(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Hl(e,t,n,r,o){var i=Pe(n)?en:ge.current;return i=An(t,i),Pn(t,o),n=Ks(e,t,n,r,i,o),r=Qs(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,_t(e,t,o)):(H&&r&&Ds(t),t.flags|=1,ye(e,t,n,o),t.child)}function hu(e,t,n,r,o){if(Pe(n)){var i=!0;Vo(t)}else i=!1;if(Pn(t,o),t.stateNode===null)wo(e,t),$d(t,n,r),Fl(t,n,r,o),r=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var a=l.context,u=n.contextType;typeof u=="object"&&u!==null?u=Fe(u):(u=Pe(n)?en:ge.current,u=An(t,u));var d=n.getDerivedStateFromProps,f=typeof d=="function"||typeof l.getSnapshotBeforeUpdate=="function";f||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==r||a!==u)&&uu(t,l,r,u),kt=!1;var m=t.memoizedState;l.state=m,Fo(t,r,l,o),a=t.memoizedState,s!==r||m!==a||Ce.current||kt?(typeof d=="function"&&(Ul(t,n,d,r),a=t.memoizedState),(s=kt||au(t,n,s,r,m,a,u))?(f||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),l.props=r,l.state=a,l.context=u,r=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Ed(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:We(t.type,s),l.props=u,f=t.pendingProps,m=l.context,a=n.contextType,typeof a=="object"&&a!==null?a=Fe(a):(a=Pe(n)?en:ge.current,a=An(t,a));var g=n.getDerivedStateFromProps;(d=typeof g=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==f||m!==a)&&uu(t,l,r,a),kt=!1,m=t.memoizedState,l.state=m,Fo(t,r,l,o);var v=t.memoizedState;s!==f||m!==v||Ce.current||kt?(typeof g=="function"&&(Ul(t,n,g,r),v=t.memoizedState),(u=kt||au(t,n,u,r,m,v,a)||!1)?(d||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,v,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,v,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),l.props=r,l.state=v,l.context=a,r=u):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return Wl(e,t,n,r,i,o)}function Wl(e,t,n,r,o,i){Wd(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return o&&eu(t,n,!1),_t(e,t,i);r=t.stateNode,fh.current=t;var s=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=bn(t,e.child,null,i),t.child=bn(t,null,s,i)):ye(e,t,s,i),t.memoizedState=r.state,o&&eu(t,n,!0),t.child}function Kd(e){var t=e.stateNode;t.pendingContext?Za(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Za(e,t.context,!1),Fs(e,t.containerInfo)}function gu(e,t,n,r,o){return Nn(),Ms(o),t.flags|=256,ye(e,t,n,r),t.child}var Kl={dehydrated:null,treeContext:null,retryLane:0};function Ql(e){return{baseLanes:e,cachePool:null,transitions:null}}function Qd(e,t,n){var r=t.pendingProps,o=W.current,i=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(o&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),U(W,o&1),e===null)return Bl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,i?(r=t.mode,i=t.child,l={mode:"hidden",children:l},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=vi(l,r,0,null),e=Zt(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ql(n),t.memoizedState=Kl,e):Xs(t,l));if(o=e.memoizedState,o!==null&&(s=o.dehydrated,s!==null))return mh(e,t,l,r,s,o,n);if(i){i=r.fallback,l=t.mode,o=e.child,s=o.sibling;var a={mode:"hidden",children:r.children};return!(l&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=jt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),s!==null?i=jt(s,i):(i=Zt(i,l,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,l=e.child.memoizedState,l=l===null?Ql(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~n,t.memoizedState=Kl,r}return i=e.child,e=i.sibling,r=jt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Xs(e,t){return t=vi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function io(e,t,n,r){return r!==null&&Ms(r),bn(t,e.child,null,n),e=Xs(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function mh(e,t,n,r,o,i,l){if(n)return t.flags&256?(t.flags&=-257,r=Ji(Error(k(422))),io(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=vi({mode:"visible",children:r.children},o,0,null),i=Zt(i,o,l,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&bn(t,e.child,null,l),t.child.memoizedState=Ql(l),t.memoizedState=Kl,i);if(!(t.mode&1))return io(e,t,l,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(k(419)),r=Ji(i,r,void 0),io(e,t,l,r)}if(s=(l&e.childLanes)!==0,ke||s){if(r=se,r!==null){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|l)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,gt(e,o),Ye(r,e,o,-1))}return ra(),r=Ji(Error(k(421))),io(e,t,l,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Rh.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ae=Nt(o.nextSibling),Ne=t,H=!0,Qe=null,e!==null&&(Ve[ze++]=ut,Ve[ze++]=ct,Ve[ze++]=tn,ut=e.id,ct=e.overflow,tn=t),t=Xs(t,r.children),t.flags|=4096,t)}function _u(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),$l(e.return,t,n)}function Zi(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function qd(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(ye(e,t,r.children,n),r=W.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&_u(e,n,t);else if(e.tag===19)_u(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(U(W,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Go(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Zi(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Go(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Zi(t,!0,n,null,i);break;case"together":Zi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function wo(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function _t(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),rn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=jt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=jt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function hh(e,t,n){switch(t.tag){case 3:Kd(t),Nn();break;case 5:Sd(t);break;case 1:Pe(t.type)&&Vo(t);break;case 4:Fs(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;U($o,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(U(W,W.current&1),t.flags|=128,null):n&t.child.childLanes?Qd(e,t,n):(U(W,W.current&1),e=_t(e,t,n),e!==null?e.sibling:null);U(W,W.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return qd(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),U(W,W.current),r)break;return null;case 22:case 23:return t.lanes=0,Hd(e,t,n)}return _t(e,t,n)}var Yd,ql,Xd,Jd;Yd=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ql=function(){};Xd=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,qt(ot.current);var i=null;switch(n){case"input":o=_l(e,o),r=_l(e,r),i=[];break;case"select":o=Q({},o,{value:void 0}),r=Q({},r,{value:void 0}),i=[];break;case"textarea":o=El(e,o),r=El(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=jo)}wl(n,r);var l;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var s=o[u];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(mr.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(s=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==s&&(a!=null||s!=null))if(u==="style")if(s){for(l in s)!s.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in a)a.hasOwnProperty(l)&&s[l]!==a[l]&&(n||(n={}),n[l]=a[l])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,s=s?s.__html:void 0,a!=null&&s!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(mr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&F("scroll",e),i||s===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Jd=function(e,t,n,r){n!==r&&(t.flags|=4)};function Yn(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function fe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function gh(e,t,n){var r=t.pendingProps;switch(js(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return fe(t),null;case 1:return Pe(t.type)&&Mo(),fe(t),null;case 3:return r=t.stateNode,On(),G(Ce),G(ge),Hs(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ro(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Qe!==null&&(rs(Qe),Qe=null))),ql(e,t),fe(t),null;case 5:Gs(t);var o=qt(Pr.current);if(n=t.type,e!==null&&t.stateNode!=null)Xd(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return fe(t),null}if(e=qt(ot.current),ro(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[nt]=t,r[kr]=i,e=(t.mode&1)!==0,n){case"dialog":F("cancel",r),F("close",r);break;case"iframe":case"object":case"embed":F("load",r);break;case"video":case"audio":for(o=0;o<nr.length;o++)F(nr[o],r);break;case"source":F("error",r);break;case"img":case"image":case"link":F("error",r),F("load",r);break;case"details":F("toggle",r);break;case"input":Pa(r,i),F("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},F("invalid",r);break;case"textarea":La(r,i),F("invalid",r)}wl(n,i),o=null;for(var l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&no(r.textContent,s,e),o=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&no(r.textContent,s,e),o=["children",""+s]):mr.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&F("scroll",r)}switch(n){case"input":Qr(r),Ra(r,i,!0);break;case"textarea":Qr(r),Ta(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=jo)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Pc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[nt]=t,e[kr]=r,Yd(e,t,!1,!1),t.stateNode=e;e:{switch(l=xl(n,r),n){case"dialog":F("cancel",e),F("close",e),o=r;break;case"iframe":case"object":case"embed":F("load",e),o=r;break;case"video":case"audio":for(o=0;o<nr.length;o++)F(nr[o],e);o=r;break;case"source":F("error",e),o=r;break;case"img":case"image":case"link":F("error",e),F("load",e),o=r;break;case"details":F("toggle",e),o=r;break;case"input":Pa(e,r),o=_l(e,r),F("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=Q({},r,{value:void 0}),F("invalid",e);break;case"textarea":La(e,r),o=El(e,r),F("invalid",e);break;default:o=r}wl(n,o),s=o;for(i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="style"?Tc(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Rc(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&hr(e,a):typeof a=="number"&&hr(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(mr.hasOwnProperty(i)?a!=null&&i==="onScroll"&&F("scroll",e):a!=null&&Ss(e,i,a,l))}switch(n){case"input":Qr(e),Ra(e,r,!1);break;case"textarea":Qr(e),Ta(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Mt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?wn(e,!!r.multiple,i,!1):r.defaultValue!=null&&wn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=jo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return fe(t),null;case 6:if(e&&t.stateNode!=null)Jd(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=qt(Pr.current),qt(ot.current),ro(t)){if(r=t.stateNode,n=t.memoizedProps,r[nt]=t,(i=r.nodeValue!==n)&&(e=Ne,e!==null))switch(e.tag){case 3:no(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&no(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[nt]=t,t.stateNode=r}return fe(t),null;case 13:if(G(W),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&Ae!==null&&t.mode&1&&!(t.flags&128))gd(),Nn(),t.flags|=98560,i=!1;else if(i=ro(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(k(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(k(317));i[nt]=t}else Nn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;fe(t),i=!1}else Qe!==null&&(rs(Qe),Qe=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||W.current&1?ne===0&&(ne=3):ra())),t.updateQueue!==null&&(t.flags|=4),fe(t),null);case 4:return On(),ql(e,t),e===null&&wr(t.stateNode.containerInfo),fe(t),null;case 10:return Bs(t.type._context),fe(t),null;case 17:return Pe(t.type)&&Mo(),fe(t),null;case 19:if(G(W),i=t.memoizedState,i===null)return fe(t),null;if(r=(t.flags&128)!==0,l=i.rendering,l===null)if(r)Yn(i,!1);else{if(ne!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Go(e),l!==null){for(t.flags|=128,Yn(i,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return U(W,W.current&1|2),t.child}e=e.sibling}i.tail!==null&&X()>jn&&(t.flags|=128,r=!0,Yn(i,!1),t.lanes=4194304)}else{if(!r)if(e=Go(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Yn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!H)return fe(t),null}else 2*X()-i.renderingStartTime>jn&&n!==1073741824&&(t.flags|=128,r=!0,Yn(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(n=i.last,n!==null?n.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=X(),t.sibling=null,n=W.current,U(W,r?n&1|2:n&1),t):(fe(t),null);case 22:case 23:return na(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ie&1073741824&&(fe(t),t.subtreeFlags&6&&(t.flags|=8192)):fe(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function _h(e,t){switch(js(t),t.tag){case 1:return Pe(t.type)&&Mo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return On(),G(Ce),G(ge),Hs(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Gs(t),null;case 13:if(G(W),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));Nn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return G(W),null;case 4:return On(),null;case 10:return Bs(t.type._context),null;case 22:case 23:return na(),null;case 24:return null;default:return null}}var lo=!1,he=!1,yh=typeof WeakSet=="function"?WeakSet:Set,T=null;function En(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){q(e,t,r)}else n.current=null}function Yl(e,t,n){try{n()}catch(r){q(e,t,r)}}var yu=!1;function vh(e,t){if(bl=bo,e=rd(),Os(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var l=0,s=-1,a=-1,u=0,d=0,f=e,m=null;t:for(;;){for(var g;f!==n||o!==0&&f.nodeType!==3||(s=l+o),f!==i||r!==0&&f.nodeType!==3||(a=l+r),f.nodeType===3&&(l+=f.nodeValue.length),(g=f.firstChild)!==null;)m=f,f=g;for(;;){if(f===e)break t;if(m===n&&++u===o&&(s=l),m===i&&++d===r&&(a=l),(g=f.nextSibling)!==null)break;f=m,m=f.parentNode}f=g}n=s===-1||a===-1?null:{start:s,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ol={focusedElem:e,selectionRange:n},bo=!1,T=t;T!==null;)if(t=T,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,T=e;else for(;T!==null;){t=T;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var E=v.memoizedProps,w=v.memoizedState,h=t.stateNode,c=h.getSnapshotBeforeUpdate(t.elementType===t.type?E:We(t.type,E),w);h.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var _=t.stateNode.containerInfo;_.nodeType===1?_.textContent="":_.nodeType===9&&_.documentElement&&_.removeChild(_.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(S){q(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,T=e;break}T=t.return}return v=yu,yu=!1,v}function cr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Yl(t,n,i)}o=o.next}while(o!==r)}}function _i(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Xl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Zd(e){var t=e.alternate;t!==null&&(e.alternate=null,Zd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[nt],delete t[kr],delete t[Ml],delete t[th],delete t[nh])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ep(e){return e.tag===5||e.tag===3||e.tag===4}function vu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ep(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Jl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=jo));else if(r!==4&&(e=e.child,e!==null))for(Jl(e,t,n),e=e.sibling;e!==null;)Jl(e,t,n),e=e.sibling}function Zl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Zl(e,t,n),e=e.sibling;e!==null;)Zl(e,t,n),e=e.sibling}var ue=null,Ke=!1;function wt(e,t,n){for(n=n.child;n!==null;)tp(e,t,n),n=n.sibling}function tp(e,t,n){if(rt&&typeof rt.onCommitFiberUnmount=="function")try{rt.onCommitFiberUnmount(ui,n)}catch{}switch(n.tag){case 5:he||En(n,t);case 6:var r=ue,o=Ke;ue=null,wt(e,t,n),ue=r,Ke=o,ue!==null&&(Ke?(e=ue,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ue.removeChild(n.stateNode));break;case 18:ue!==null&&(Ke?(e=ue,n=n.stateNode,e.nodeType===8?Wi(e.parentNode,n):e.nodeType===1&&Wi(e,n),vr(e)):Wi(ue,n.stateNode));break;case 4:r=ue,o=Ke,ue=n.stateNode.containerInfo,Ke=!0,wt(e,t,n),ue=r,Ke=o;break;case 0:case 11:case 14:case 15:if(!he&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&Yl(n,t,l),o=o.next}while(o!==r)}wt(e,t,n);break;case 1:if(!he&&(En(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){q(n,t,s)}wt(e,t,n);break;case 21:wt(e,t,n);break;case 22:n.mode&1?(he=(r=he)||n.memoizedState!==null,wt(e,t,n),he=r):wt(e,t,n);break;default:wt(e,t,n)}}function Eu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new yh),t.forEach(function(r){var o=Lh.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function He(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:ue=s.stateNode,Ke=!1;break e;case 3:ue=s.stateNode.containerInfo,Ke=!0;break e;case 4:ue=s.stateNode.containerInfo,Ke=!0;break e}s=s.return}if(ue===null)throw Error(k(160));tp(i,l,o),ue=null,Ke=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){q(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)np(t,e),t=t.sibling}function np(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(He(t,e),Je(e),r&4){try{cr(3,e,e.return),_i(3,e)}catch(E){q(e,e.return,E)}try{cr(5,e,e.return)}catch(E){q(e,e.return,E)}}break;case 1:He(t,e),Je(e),r&512&&n!==null&&En(n,n.return);break;case 5:if(He(t,e),Je(e),r&512&&n!==null&&En(n,n.return),e.flags&32){var o=e.stateNode;try{hr(o,"")}catch(E){q(e,e.return,E)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,l=n!==null?n.memoizedProps:i,s=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&kc(o,i),xl(s,l);var u=xl(s,i);for(l=0;l<a.length;l+=2){var d=a[l],f=a[l+1];d==="style"?Tc(o,f):d==="dangerouslySetInnerHTML"?Rc(o,f):d==="children"?hr(o,f):Ss(o,d,f,u)}switch(s){case"input":yl(o,i);break;case"textarea":Cc(o,i);break;case"select":var m=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?wn(o,!!i.multiple,g,!1):m!==!!i.multiple&&(i.defaultValue!=null?wn(o,!!i.multiple,i.defaultValue,!0):wn(o,!!i.multiple,i.multiple?[]:"",!1))}o[kr]=i}catch(E){q(e,e.return,E)}}break;case 6:if(He(t,e),Je(e),r&4){if(e.stateNode===null)throw Error(k(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(E){q(e,e.return,E)}}break;case 3:if(He(t,e),Je(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{vr(t.containerInfo)}catch(E){q(e,e.return,E)}break;case 4:He(t,e),Je(e);break;case 13:He(t,e),Je(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(ea=X())),r&4&&Eu(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(he=(u=he)||d,He(t,e),he=u):He(t,e),Je(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(T=e,d=e.child;d!==null;){for(f=T=d;T!==null;){switch(m=T,g=m.child,m.tag){case 0:case 11:case 14:case 15:cr(4,m,m.return);break;case 1:En(m,m.return);var v=m.stateNode;if(typeof v.componentWillUnmount=="function"){r=m,n=m.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(E){q(r,n,E)}}break;case 5:En(m,m.return);break;case 22:if(m.memoizedState!==null){wu(f);continue}}g!==null?(g.return=m,T=g):wu(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{o=f.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=f.stateNode,a=f.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,s.style.display=Lc("display",l))}catch(E){q(e,e.return,E)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(E){q(e,e.return,E)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:He(t,e),Je(e),r&4&&Eu(e);break;case 21:break;default:He(t,e),Je(e)}}function Je(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ep(n)){var r=n;break e}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(hr(o,""),r.flags&=-33);var i=vu(e);Zl(e,i,o);break;case 3:case 4:var l=r.stateNode.containerInfo,s=vu(e);Jl(e,s,l);break;default:throw Error(k(161))}}catch(a){q(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Eh(e,t,n){T=e,rp(e)}function rp(e,t,n){for(var r=(e.mode&1)!==0;T!==null;){var o=T,i=o.child;if(o.tag===22&&r){var l=o.memoizedState!==null||lo;if(!l){var s=o.alternate,a=s!==null&&s.memoizedState!==null||he;s=lo;var u=he;if(lo=l,(he=a)&&!u)for(T=o;T!==null;)l=T,a=l.child,l.tag===22&&l.memoizedState!==null?xu(o):a!==null?(a.return=l,T=a):xu(o);for(;i!==null;)T=i,rp(i),i=i.sibling;T=o,lo=s,he=u}Su(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,T=i):Su(e)}}function Su(e){for(;T!==null;){var t=T;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:he||_i(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!he)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:We(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&iu(t,i,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}iu(t,l,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&vr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}he||t.flags&512&&Xl(t)}catch(m){q(t,t.return,m)}}if(t===e){T=null;break}if(n=t.sibling,n!==null){n.return=t.return,T=n;break}T=t.return}}function wu(e){for(;T!==null;){var t=T;if(t===e){T=null;break}var n=t.sibling;if(n!==null){n.return=t.return,T=n;break}T=t.return}}function xu(e){for(;T!==null;){var t=T;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{_i(4,t)}catch(a){q(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){q(t,o,a)}}var i=t.return;try{Xl(t)}catch(a){q(t,i,a)}break;case 5:var l=t.return;try{Xl(t)}catch(a){q(t,l,a)}}}catch(a){q(t,t.return,a)}if(t===e){T=null;break}var s=t.sibling;if(s!==null){s.return=t.return,T=s;break}T=t.return}}var Sh=Math.ceil,Ko=yt.ReactCurrentDispatcher,Js=yt.ReactCurrentOwner,Ue=yt.ReactCurrentBatchConfig,V=0,se=null,Z=null,ce=0,Ie=0,Sn=Bt(0),ne=0,Ir=null,rn=0,yi=0,Zs=0,dr=null,xe=null,ea=0,jn=1/0,lt=null,Qo=!1,es=null,Ot=null,so=!1,Lt=null,qo=0,pr=0,ts=null,xo=-1,ko=0;function Ee(){return V&6?X():xo!==-1?xo:xo=X()}function Dt(e){return e.mode&1?V&2&&ce!==0?ce&-ce:oh.transition!==null?(ko===0&&(ko=$c()),ko):(e=z,e!==0||(e=window.event,e=e===void 0?16:Qc(e.type)),e):1}function Ye(e,t,n,r){if(50<pr)throw pr=0,ts=null,Error(k(185));Mr(e,n,r),(!(V&2)||e!==se)&&(e===se&&(!(V&2)&&(yi|=n),ne===4&&Pt(e,ce)),Re(e,r),n===1&&V===0&&!(t.mode&1)&&(jn=X()+500,mi&&$t()))}function Re(e,t){var n=e.callbackNode;om(e,t);var r=No(e,e===se?ce:0);if(r===0)n!==null&&Na(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Na(n),t===1)e.tag===0?rh(ku.bind(null,e)):fd(ku.bind(null,e)),Zm(function(){!(V&6)&&$t()}),n=null;else{switch(Uc(r)){case 1:n=Ps;break;case 4:n=zc;break;case 16:n=Ao;break;case 536870912:n=Bc;break;default:n=Ao}n=dp(n,op.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function op(e,t){if(xo=-1,ko=0,V&6)throw Error(k(327));var n=e.callbackNode;if(Rn()&&e.callbackNode!==n)return null;var r=No(e,e===se?ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Yo(e,r);else{t=r;var o=V;V|=2;var i=lp();(se!==e||ce!==t)&&(lt=null,jn=X()+500,Jt(e,t));do try{kh();break}catch(s){ip(e,s)}while(!0);zs(),Ko.current=i,V=o,Z!==null?t=0:(se=null,ce=0,t=ne)}if(t!==0){if(t===2&&(o=Ll(e),o!==0&&(r=o,t=ns(e,o))),t===1)throw n=Ir,Jt(e,0),Pt(e,r),Re(e,X()),n;if(t===6)Pt(e,r);else{if(o=e.current.alternate,!(r&30)&&!wh(o)&&(t=Yo(e,r),t===2&&(i=Ll(e),i!==0&&(r=i,t=ns(e,i))),t===1))throw n=Ir,Jt(e,0),Pt(e,r),Re(e,X()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:Wt(e,xe,lt);break;case 3:if(Pt(e,r),(r&130023424)===r&&(t=ea+500-X(),10<t)){if(No(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ee(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=jl(Wt.bind(null,e,xe,lt),t);break}Wt(e,xe,lt);break;case 4:if(Pt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-qe(r);i=1<<l,l=t[l],l>o&&(o=l),r&=~i}if(r=o,r=X()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Sh(r/1960))-r,10<r){e.timeoutHandle=jl(Wt.bind(null,e,xe,lt),r);break}Wt(e,xe,lt);break;case 5:Wt(e,xe,lt);break;default:throw Error(k(329))}}}return Re(e,X()),e.callbackNode===n?op.bind(null,e):null}function ns(e,t){var n=dr;return e.current.memoizedState.isDehydrated&&(Jt(e,t).flags|=256),e=Yo(e,t),e!==2&&(t=xe,xe=n,t!==null&&rs(t)),e}function rs(e){xe===null?xe=e:xe.push.apply(xe,e)}function wh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Xe(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Pt(e,t){for(t&=~Zs,t&=~yi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-qe(t),r=1<<n;e[n]=-1,t&=~r}}function ku(e){if(V&6)throw Error(k(327));Rn();var t=No(e,0);if(!(t&1))return Re(e,X()),null;var n=Yo(e,t);if(e.tag!==0&&n===2){var r=Ll(e);r!==0&&(t=r,n=ns(e,r))}if(n===1)throw n=Ir,Jt(e,0),Pt(e,t),Re(e,X()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Wt(e,xe,lt),Re(e,X()),null}function ta(e,t){var n=V;V|=1;try{return e(t)}finally{V=n,V===0&&(jn=X()+500,mi&&$t())}}function on(e){Lt!==null&&Lt.tag===0&&!(V&6)&&Rn();var t=V;V|=1;var n=Ue.transition,r=z;try{if(Ue.transition=null,z=1,e)return e()}finally{z=r,Ue.transition=n,V=t,!(V&6)&&$t()}}function na(){Ie=Sn.current,G(Sn)}function Jt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Jm(n)),Z!==null)for(n=Z.return;n!==null;){var r=n;switch(js(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Mo();break;case 3:On(),G(Ce),G(ge),Hs();break;case 5:Gs(r);break;case 4:On();break;case 13:G(W);break;case 19:G(W);break;case 10:Bs(r.type._context);break;case 22:case 23:na()}n=n.return}if(se=e,Z=e=jt(e.current,null),ce=Ie=t,ne=0,Ir=null,Zs=yi=rn=0,xe=dr=null,Qt!==null){for(t=0;t<Qt.length;t++)if(n=Qt[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var l=i.next;i.next=o,r.next=l}n.pending=r}Qt=null}return e}function ip(e,t){do{var n=Z;try{if(zs(),Eo.current=Wo,Ho){for(var r=K.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ho=!1}if(nn=0,le=ee=K=null,ur=!1,Rr=0,Js.current=null,n===null||n.return===null){ne=1,Ir=t,Z=null;break}e:{var i=e,l=n.return,s=n,a=t;if(t=ce,s.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,d=s,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var m=d.alternate;m?(d.updateQueue=m.updateQueue,d.memoizedState=m.memoizedState,d.lanes=m.lanes):(d.updateQueue=null,d.memoizedState=null)}var g=du(l);if(g!==null){g.flags&=-257,pu(g,l,s,i,t),g.mode&1&&cu(i,u,t),t=g,a=u;var v=t.updateQueue;if(v===null){var E=new Set;E.add(a),t.updateQueue=E}else v.add(a);break e}else{if(!(t&1)){cu(i,u,t),ra();break e}a=Error(k(426))}}else if(H&&s.mode&1){var w=du(l);if(w!==null){!(w.flags&65536)&&(w.flags|=256),pu(w,l,s,i,t),Ms(Dn(a,s));break e}}i=a=Dn(a,s),ne!==4&&(ne=2),dr===null?dr=[i]:dr.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=Ud(i,a,t);ou(i,h);break e;case 1:s=a;var c=i.type,_=i.stateNode;if(!(i.flags&128)&&(typeof c.getDerivedStateFromError=="function"||_!==null&&typeof _.componentDidCatch=="function"&&(Ot===null||!Ot.has(_)))){i.flags|=65536,t&=-t,i.lanes|=t;var S=Fd(i,s,t);ou(i,S);break e}}i=i.return}while(i!==null)}ap(n)}catch(C){t=C,Z===n&&n!==null&&(Z=n=n.return);continue}break}while(!0)}function lp(){var e=Ko.current;return Ko.current=Wo,e===null?Wo:e}function ra(){(ne===0||ne===3||ne===2)&&(ne=4),se===null||!(rn&268435455)&&!(yi&268435455)||Pt(se,ce)}function Yo(e,t){var n=V;V|=2;var r=lp();(se!==e||ce!==t)&&(lt=null,Jt(e,t));do try{xh();break}catch(o){ip(e,o)}while(!0);if(zs(),V=n,Ko.current=r,Z!==null)throw Error(k(261));return se=null,ce=0,ne}function xh(){for(;Z!==null;)sp(Z)}function kh(){for(;Z!==null&&!qf();)sp(Z)}function sp(e){var t=cp(e.alternate,e,Ie);e.memoizedProps=e.pendingProps,t===null?ap(e):Z=t,Js.current=null}function ap(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=_h(n,t),n!==null){n.flags&=32767,Z=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ne=6,Z=null;return}}else if(n=gh(n,t,Ie),n!==null){Z=n;return}if(t=t.sibling,t!==null){Z=t;return}Z=t=e}while(t!==null);ne===0&&(ne=5)}function Wt(e,t,n){var r=z,o=Ue.transition;try{Ue.transition=null,z=1,Ch(e,t,n,r)}finally{Ue.transition=o,z=r}return null}function Ch(e,t,n,r){do Rn();while(Lt!==null);if(V&6)throw Error(k(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(im(e,i),e===se&&(Z=se=null,ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||so||(so=!0,dp(Ao,function(){return Rn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ue.transition,Ue.transition=null;var l=z;z=1;var s=V;V|=4,Js.current=null,vh(e,n),np(n,e),Hm(Ol),bo=!!bl,Ol=bl=null,e.current=n,Eh(n),Yf(),V=s,z=l,Ue.transition=i}else e.current=n;if(so&&(so=!1,Lt=e,qo=o),i=e.pendingLanes,i===0&&(Ot=null),Zf(n.stateNode),Re(e,X()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Qo)throw Qo=!1,e=es,es=null,e;return qo&1&&e.tag!==0&&Rn(),i=e.pendingLanes,i&1?e===ts?pr++:(pr=0,ts=e):pr=0,$t(),null}function Rn(){if(Lt!==null){var e=Uc(qo),t=Ue.transition,n=z;try{if(Ue.transition=null,z=16>e?16:e,Lt===null)var r=!1;else{if(e=Lt,Lt=null,qo=0,V&6)throw Error(k(331));var o=V;for(V|=4,T=e.current;T!==null;){var i=T,l=i.child;if(T.flags&16){var s=i.deletions;if(s!==null){for(var a=0;a<s.length;a++){var u=s[a];for(T=u;T!==null;){var d=T;switch(d.tag){case 0:case 11:case 15:cr(8,d,i)}var f=d.child;if(f!==null)f.return=d,T=f;else for(;T!==null;){d=T;var m=d.sibling,g=d.return;if(Zd(d),d===u){T=null;break}if(m!==null){m.return=g,T=m;break}T=g}}}var v=i.alternate;if(v!==null){var E=v.child;if(E!==null){v.child=null;do{var w=E.sibling;E.sibling=null,E=w}while(E!==null)}}T=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,T=l;else e:for(;T!==null;){if(i=T,i.flags&2048)switch(i.tag){case 0:case 11:case 15:cr(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,T=h;break e}T=i.return}}var c=e.current;for(T=c;T!==null;){l=T;var _=l.child;if(l.subtreeFlags&2064&&_!==null)_.return=l,T=_;else e:for(l=c;T!==null;){if(s=T,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:_i(9,s)}}catch(C){q(s,s.return,C)}if(s===l){T=null;break e}var S=s.sibling;if(S!==null){S.return=s.return,T=S;break e}T=s.return}}if(V=o,$t(),rt&&typeof rt.onPostCommitFiberRoot=="function")try{rt.onPostCommitFiberRoot(ui,e)}catch{}r=!0}return r}finally{z=n,Ue.transition=t}}return!1}function Cu(e,t,n){t=Dn(n,t),t=Ud(e,t,1),e=bt(e,t,1),t=Ee(),e!==null&&(Mr(e,1,t),Re(e,t))}function q(e,t,n){if(e.tag===3)Cu(e,e,n);else for(;t!==null;){if(t.tag===3){Cu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ot===null||!Ot.has(r))){e=Dn(n,e),e=Fd(t,e,1),t=bt(t,e,1),e=Ee(),t!==null&&(Mr(t,1,e),Re(t,e));break}}t=t.return}}function Ph(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ee(),e.pingedLanes|=e.suspendedLanes&n,se===e&&(ce&n)===n&&(ne===4||ne===3&&(ce&130023424)===ce&&500>X()-ea?Jt(e,0):Zs|=n),Re(e,t)}function up(e,t){t===0&&(e.mode&1?(t=Xr,Xr<<=1,!(Xr&130023424)&&(Xr=4194304)):t=1);var n=Ee();e=gt(e,t),e!==null&&(Mr(e,t,n),Re(e,n))}function Rh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),up(e,n)}function Lh(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),up(e,n)}var cp;cp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ce.current)ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ke=!1,hh(e,t,n);ke=!!(e.flags&131072)}else ke=!1,H&&t.flags&1048576&&md(t,Bo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;wo(e,t),e=t.pendingProps;var o=An(t,ge.current);Pn(t,n),o=Ks(null,t,r,e,o,n);var i=Qs();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pe(r)?(i=!0,Vo(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Us(t),o.updater=gi,t.stateNode=o,o._reactInternals=t,Fl(t,r,e,n),t=Wl(null,t,r,!0,i,n)):(t.tag=0,H&&i&&Ds(t),ye(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(wo(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Ih(r),e=We(r,e),o){case 0:t=Hl(null,t,r,e,n);break e;case 1:t=hu(null,t,r,e,n);break e;case 11:t=fu(null,t,r,e,n);break e;case 14:t=mu(null,t,r,We(r.type,e),n);break e}throw Error(k(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:We(r,o),Hl(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:We(r,o),hu(e,t,r,o,n);case 3:e:{if(Kd(t),e===null)throw Error(k(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Ed(e,t),Fo(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Dn(Error(k(423)),t),t=gu(e,t,r,n,o);break e}else if(r!==o){o=Dn(Error(k(424)),t),t=gu(e,t,r,n,o);break e}else for(Ae=Nt(t.stateNode.containerInfo.firstChild),Ne=t,H=!0,Qe=null,n=yd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Nn(),r===o){t=_t(e,t,n);break e}ye(e,t,r,n)}t=t.child}return t;case 5:return Sd(t),e===null&&Bl(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,l=o.children,Dl(r,o)?l=null:i!==null&&Dl(r,i)&&(t.flags|=32),Wd(e,t),ye(e,t,l,n),t.child;case 6:return e===null&&Bl(t),null;case 13:return Qd(e,t,n);case 4:return Fs(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=bn(t,null,r,n):ye(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:We(r,o),fu(e,t,r,o,n);case 7:return ye(e,t,t.pendingProps,n),t.child;case 8:return ye(e,t,t.pendingProps.children,n),t.child;case 12:return ye(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,U($o,r._currentValue),r._currentValue=l,i!==null)if(Xe(i.value,l)){if(i.children===o.children&&!Ce.current){t=_t(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){l=i.child;for(var a=s.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=pt(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?a.next=a:(a.next=d.next,d.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),$l(i.return,n,t),s.lanes|=n;break}a=a.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(k(341));l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),$l(l,n,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}ye(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Pn(t,n),o=Fe(o),r=r(o),t.flags|=1,ye(e,t,r,n),t.child;case 14:return r=t.type,o=We(r,t.pendingProps),o=We(r.type,o),mu(e,t,r,o,n);case 15:return Gd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:We(r,o),wo(e,t),t.tag=1,Pe(r)?(e=!0,Vo(t)):e=!1,Pn(t,n),$d(t,r,o),Fl(t,r,o,n),Wl(null,t,r,!0,e,n);case 19:return qd(e,t,n);case 22:return Hd(e,t,n)}throw Error(k(156,t.tag))};function dp(e,t){return Vc(e,t)}function Th(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function $e(e,t,n,r){return new Th(e,t,n,r)}function oa(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ih(e){if(typeof e=="function")return oa(e)?1:0;if(e!=null){if(e=e.$$typeof,e===xs)return 11;if(e===ks)return 14}return 2}function jt(e,t){var n=e.alternate;return n===null?(n=$e(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Co(e,t,n,r,o,i){var l=2;if(r=e,typeof e=="function")oa(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case dn:return Zt(n.children,o,i,t);case ws:l=8,o|=8;break;case fl:return e=$e(12,n,t,o|2),e.elementType=fl,e.lanes=i,e;case ml:return e=$e(13,n,t,o),e.elementType=ml,e.lanes=i,e;case hl:return e=$e(19,n,t,o),e.elementType=hl,e.lanes=i,e;case Sc:return vi(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case vc:l=10;break e;case Ec:l=9;break e;case xs:l=11;break e;case ks:l=14;break e;case xt:l=16,r=null;break e}throw Error(k(130,e==null?e:typeof e,""))}return t=$e(l,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Zt(e,t,n,r){return e=$e(7,e,r,t),e.lanes=n,e}function vi(e,t,n,r){return e=$e(22,e,r,t),e.elementType=Sc,e.lanes=n,e.stateNode={isHidden:!1},e}function el(e,t,n){return e=$e(6,e,null,t),e.lanes=n,e}function tl(e,t,n){return t=$e(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ah(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Di(0),this.expirationTimes=Di(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Di(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function ia(e,t,n,r,o,i,l,s,a){return e=new Ah(e,t,n,s,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=$e(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Us(i),e}function Nh(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:cn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function pp(e){if(!e)return Vt;e=e._reactInternals;e:{if(sn(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(Pe(n))return pd(e,n,t)}return t}function fp(e,t,n,r,o,i,l,s,a){return e=ia(n,r,!0,e,o,i,l,s,a),e.context=pp(null),n=e.current,r=Ee(),o=Dt(n),i=pt(r,o),i.callback=t??null,bt(n,i,o),e.current.lanes=o,Mr(e,o,r),Re(e,r),e}function Ei(e,t,n,r){var o=t.current,i=Ee(),l=Dt(o);return n=pp(n),t.context===null?t.context=n:t.pendingContext=n,t=pt(i,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=bt(o,t,l),e!==null&&(Ye(e,o,l,i),vo(e,o,l)),l}function Xo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Pu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function la(e,t){Pu(e,t),(e=e.alternate)&&Pu(e,t)}function bh(){return null}var mp=typeof reportError=="function"?reportError:function(e){console.error(e)};function sa(e){this._internalRoot=e}Si.prototype.render=sa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));Ei(e,t,null,null)};Si.prototype.unmount=sa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;on(function(){Ei(null,e,null,null)}),t[ht]=null}};function Si(e){this._internalRoot=e}Si.prototype.unstable_scheduleHydration=function(e){if(e){var t=Hc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ct.length&&t!==0&&t<Ct[n].priority;n++);Ct.splice(n,0,e),n===0&&Kc(e)}};function aa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function wi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ru(){}function Oh(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Xo(l);i.call(u)}}var l=fp(t,r,e,0,null,!1,!1,"",Ru);return e._reactRootContainer=l,e[ht]=l.current,wr(e.nodeType===8?e.parentNode:e),on(),l}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var s=r;r=function(){var u=Xo(a);s.call(u)}}var a=ia(e,0,!1,null,null,!1,!1,"",Ru);return e._reactRootContainer=a,e[ht]=a.current,wr(e.nodeType===8?e.parentNode:e),on(function(){Ei(t,a,n,r)}),a}function xi(e,t,n,r,o){var i=n._reactRootContainer;if(i){var l=i;if(typeof o=="function"){var s=o;o=function(){var a=Xo(l);s.call(a)}}Ei(t,l,e,o)}else l=Oh(n,t,e,o,r);return Xo(l)}Fc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=tr(t.pendingLanes);n!==0&&(Rs(t,n|1),Re(t,X()),!(V&6)&&(jn=X()+500,$t()))}break;case 13:on(function(){var r=gt(e,1);if(r!==null){var o=Ee();Ye(r,e,1,o)}}),la(e,1)}};Ls=function(e){if(e.tag===13){var t=gt(e,134217728);if(t!==null){var n=Ee();Ye(t,e,134217728,n)}la(e,134217728)}};Gc=function(e){if(e.tag===13){var t=Dt(e),n=gt(e,t);if(n!==null){var r=Ee();Ye(n,e,t,r)}la(e,t)}};Hc=function(){return z};Wc=function(e,t){var n=z;try{return z=e,t()}finally{z=n}};Cl=function(e,t,n){switch(t){case"input":if(yl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=fi(r);if(!o)throw Error(k(90));xc(r),yl(r,o)}}}break;case"textarea":Cc(e,n);break;case"select":t=n.value,t!=null&&wn(e,!!n.multiple,t,!1)}};Nc=ta;bc=on;var Dh={usingClientEntryPoint:!1,Events:[zr,hn,fi,Ic,Ac,ta]},Xn={findFiberByHostInstance:Kt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},jh={bundleType:Xn.bundleType,version:Xn.version,rendererPackageName:Xn.rendererPackageName,rendererConfig:Xn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:yt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=jc(e),e===null?null:e.stateNode},findFiberByHostInstance:Xn.findFiberByHostInstance||bh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ao=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ao.isDisabled&&ao.supportsFiber)try{ui=ao.inject(jh),rt=ao}catch{}}Oe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Dh;Oe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!aa(t))throw Error(k(200));return Nh(e,t,null,n)};Oe.createRoot=function(e,t){if(!aa(e))throw Error(k(299));var n=!1,r="",o=mp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=ia(e,1,!1,null,null,n,!1,r,o),e[ht]=t.current,wr(e.nodeType===8?e.parentNode:e),new sa(t)};Oe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=jc(t),e=e===null?null:e.stateNode,e};Oe.flushSync=function(e){return on(e)};Oe.hydrate=function(e,t,n){if(!wi(t))throw Error(k(200));return xi(null,e,t,!0,n)};Oe.hydrateRoot=function(e,t,n){if(!aa(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",l=mp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=fp(t,null,e,1,n??null,o,!1,i,l),e[ht]=t.current,wr(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Si(t)};Oe.render=function(e,t,n){if(!wi(t))throw Error(k(200));return xi(null,e,t,!1,n)};Oe.unmountComponentAtNode=function(e){if(!wi(e))throw Error(k(40));return e._reactRootContainer?(on(function(){xi(null,null,e,!1,function(){e._reactRootContainer=null,e[ht]=null})}),!0):!1};Oe.unstable_batchedUpdates=ta;Oe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!wi(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return xi(e,t,n,!1,r)};Oe.version="18.3.1-next-f1338f8080-20240426";function hp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(hp)}catch(e){console.error(e)}}hp(),hc.exports=Oe;var Mh=hc.exports,Lu=Mh;dl.createRoot=Lu.createRoot,dl.hydrateRoot=Lu.hydrateRoot;const Vh={},Tu=e=>{let t;const n=new Set,r=(d,f)=>{const m=typeof d=="function"?d(t):d;if(!Object.is(m,t)){const g=t;t=f??(typeof m!="object"||m===null)?m:Object.assign({},t,m),n.forEach(v=>v(t,g))}},o=()=>t,a={setState:r,getState:o,getInitialState:()=>u,subscribe:d=>(n.add(d),()=>n.delete(d)),destroy:()=>{(Vh?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=t=e(r,o,a);return a},zh=e=>e?Tu(e):Tu;var gp={exports:{}},_p={},yp={exports:{}},vp={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mn=N;function Bh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var $h=typeof Object.is=="function"?Object.is:Bh,Uh=Mn.useState,Fh=Mn.useEffect,Gh=Mn.useLayoutEffect,Hh=Mn.useDebugValue;function Wh(e,t){var n=t(),r=Uh({inst:{value:n,getSnapshot:t}}),o=r[0].inst,i=r[1];return Gh(function(){o.value=n,o.getSnapshot=t,nl(o)&&i({inst:o})},[e,n,t]),Fh(function(){return nl(o)&&i({inst:o}),e(function(){nl(o)&&i({inst:o})})},[e]),Hh(n),n}function nl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!$h(e,n)}catch{return!0}}function Kh(e,t){return t()}var Qh=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Kh:Wh;vp.useSyncExternalStore=Mn.useSyncExternalStore!==void 0?Mn.useSyncExternalStore:Qh;yp.exports=vp;var qh=yp.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ki=N,Yh=qh;function Xh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Jh=typeof Object.is=="function"?Object.is:Xh,Zh=Yh.useSyncExternalStore,eg=ki.useRef,tg=ki.useEffect,ng=ki.useMemo,rg=ki.useDebugValue;_p.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var i=eg(null);if(i.current===null){var l={hasValue:!1,value:null};i.current=l}else l=i.current;i=ng(function(){function a(g){if(!u){if(u=!0,d=g,g=r(g),o!==void 0&&l.hasValue){var v=l.value;if(o(v,g))return f=v}return f=g}if(v=f,Jh(d,g))return v;var E=r(g);return o!==void 0&&o(v,E)?(d=g,v):(d=g,f=E)}var u=!1,d,f,m=n===void 0?null:n;return[function(){return a(t())},m===null?void 0:function(){return a(m())}]},[t,n,r,o]);var s=Zh(e,i[0],i[1]);return tg(function(){l.hasValue=!0,l.value=s},[s]),rg(s),s};gp.exports=_p;var og=gp.exports;const ig=rc(og),Ep={},{useDebugValue:lg}=ys,{useSyncExternalStoreWithSelector:sg}=ig;let Iu=!1;const ag=e=>e;function ug(e,t=ag,n){(Ep?"production":void 0)!=="production"&&n&&!Iu&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Iu=!0);const r=sg(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return lg(r),r}const Au=e=>{(Ep?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?zh(e):e,n=(r,o)=>ug(t,r,o);return Object.assign(n,t),n},ua=e=>e?Au(e):Au,cg={};function dg(e,t){let n;try{n=e()}catch{return}return{getItem:o=>{var i;const l=a=>a===null?null:JSON.parse(a,void 0),s=(i=n.getItem(o))!=null?i:null;return s instanceof Promise?s.then(l):l(s)},setItem:(o,i)=>n.setItem(o,JSON.stringify(i,void 0)),removeItem:o=>n.removeItem(o)}}const Ar=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return Ar(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return Ar(r)(n)}}}},pg=(e,t)=>(n,r,o)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:w=>w,version:0,merge:(w,h)=>({...h,...w}),...t},l=!1;const s=new Set,a=new Set;let u;try{u=i.getStorage()}catch{}if(!u)return e((...w)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...w)},r,o);const d=Ar(i.serialize),f=()=>{const w=i.partialize({...r()});let h;const c=d({state:w,version:i.version}).then(_=>u.setItem(i.name,_)).catch(_=>{h=_});if(h)throw h;return c},m=o.setState;o.setState=(w,h)=>{m(w,h),f()};const g=e((...w)=>{n(...w),f()},r,o);let v;const E=()=>{var w;if(!u)return;l=!1,s.forEach(c=>c(r()));const h=((w=i.onRehydrateStorage)==null?void 0:w.call(i,r()))||void 0;return Ar(u.getItem.bind(u))(i.name).then(c=>{if(c)return i.deserialize(c)}).then(c=>{if(c)if(typeof c.version=="number"&&c.version!==i.version){if(i.migrate)return i.migrate(c.state,c.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return c.state}).then(c=>{var _;return v=i.merge(c,(_=r())!=null?_:g),n(v,!0),f()}).then(()=>{h==null||h(v,void 0),l=!0,a.forEach(c=>c(v))}).catch(c=>{h==null||h(void 0,c)})};return o.persist={setOptions:w=>{i={...i,...w},w.getStorage&&(u=w.getStorage())},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>E(),hasHydrated:()=>l,onHydrate:w=>(s.add(w),()=>{s.delete(w)}),onFinishHydration:w=>(a.add(w),()=>{a.delete(w)})},E(),v||g},fg=(e,t)=>(n,r,o)=>{let i={storage:dg(()=>localStorage),partialize:E=>E,version:0,merge:(E,w)=>({...w,...E}),...t},l=!1;const s=new Set,a=new Set;let u=i.storage;if(!u)return e((...E)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...E)},r,o);const d=()=>{const E=i.partialize({...r()});return u.setItem(i.name,{state:E,version:i.version})},f=o.setState;o.setState=(E,w)=>{f(E,w),d()};const m=e((...E)=>{n(...E),d()},r,o);o.getInitialState=()=>m;let g;const v=()=>{var E,w;if(!u)return;l=!1,s.forEach(c=>{var _;return c((_=r())!=null?_:m)});const h=((w=i.onRehydrateStorage)==null?void 0:w.call(i,(E=r())!=null?E:m))||void 0;return Ar(u.getItem.bind(u))(i.name).then(c=>{if(c)if(typeof c.version=="number"&&c.version!==i.version){if(i.migrate)return[!0,i.migrate(c.state,c.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,c.state];return[!1,void 0]}).then(c=>{var _;const[S,C]=c;if(g=i.merge(C,(_=r())!=null?_:m),n(g,!0),S)return d()}).then(()=>{h==null||h(g,void 0),g=r(),l=!0,a.forEach(c=>c(g))}).catch(c=>{h==null||h(void 0,c)})};return o.persist={setOptions:E=>{i={...i,...E},E.storage&&(u=E.storage)},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>v(),hasHydrated:()=>l,onHydrate:E=>(s.add(E),()=>{s.delete(E)}),onFinishHydration:E=>(a.add(E),()=>{a.delete(E)})},i.skipHydration||v(),g||m},mg=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((cg?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),pg(e,t)):fg(e,t),hg=mg,ae=ua()(hg((e,t)=>({providers:[{id:"openai",name:"OpenAI",isConfigured:!1},{id:"anthropic",name:"Anthropic",isConfigured:!1},{id:"gemini",name:"Google Gemini",isConfigured:!1},{id:"groq",name:"Groq",isConfigured:!1},{id:"deepseek",name:"DeepSeek",isConfigured:!1},{id:"mistral",name:"Mistral",isConfigured:!1},{id:"moonshot",name:"Moonshot AI",isConfigured:!1},{id:"openrouter",name:"OpenRouter",isConfigured:!1},{id:"perplexity",name:"Perplexity",isConfigured:!1},{id:"qwen",name:"Alibaba Qwen",isConfigured:!1},{id:"together",name:"Together AI",isConfigured:!1},{id:"vertex",name:"Google Vertex AI",isConfigured:!1},{id:"xai",name:"xAI",isConfigured:!1},{id:"ollama",name:"Ollama",isConfigured:!1},{id:"lmstudio",name:"LM Studio",isConfigured:!1}],activeProvider:"openai",setProvider:n=>{const r=t().providers.find(o=>o.id===n);e({currentProvider:r,activeProvider:n})},setModel:n=>{e({currentModel:{id:n,name:n}})},updateProviderKey:(n,r)=>e(o=>({providers:o.providers.map(i=>i.id===n?{...i,apiKey:r,isConfigured:!0}:i)}))}),{name:"sahai-settings"})),ca=ua(e=>({modal:null,openModal:t=>e({modal:t}),closeModal:()=>e({modal:null})})),gg=()=>{const{currentProvider:e}=ae();if(!e)return y.jsx("div",{className:"w-2 h-2 rounded-full bg-adobe-text-secondary/50",title:"No provider selected"});const t=e.isConfigured;return y.jsx("div",{className:`w-2 h-2 rounded-full ${t?"bg-green-500":"bg-yellow-500"}`,title:t?`${e.name} is configured`:`${e.name} needs configuration`})};/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _g=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Sp=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var yg={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vg=N.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:l,...s},a)=>N.createElement("svg",{ref:a,...yg,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Sp("lucide",o),...s},[...l.map(([u,d])=>N.createElement(u,d)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const it=(e,t)=>{const n=N.forwardRef(({className:r,...o},i)=>N.createElement(vg,{ref:i,iconNode:t,className:Sp(`lucide-${_g(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eg=it("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sg=it("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wg=it("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xg=it("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kg=it("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cg=it("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pg=it("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rg=it("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lg=it("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tg=it("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Ig=()=>{const{currentProvider:e,currentModel:t}=ae(),{openModal:n}=ca(),r=N.useMemo(()=>e?t?`${e.name}:${t.name}`:`${e.name}:Select Model`:"Select Provider",[e,t]);return y.jsxs("header",{className:"flex items-center justify-between px-3 py-2 border-b border-adobe-border bg-adobe-bg-secondary",children:[y.jsx(gg,{}),y.jsx("button",{onClick:()=>n("provider"),className:"flex-1 text-sm font-semibold text-adobe-text-primary hover:text-adobe-accent text-center mx-2",title:"Click and Select Model",children:r}),y.jsxs("div",{className:"flex items-center gap-2",children:[y.jsx("button",{onClick:()=>n("chat-history"),title:"History",children:y.jsx(xg,{size:16})}),y.jsx("button",{onClick:()=>n("settings"),title:"Settings",children:y.jsx(Tg,{size:16})}),y.jsx("button",{onClick:()=>{},title:"New chat",children:y.jsx(Rg,{size:16})})]})]})},wp=ua(e=>({messages:[],isLoading:!1,addMessage:t=>e(n=>({messages:[...n.messages,{...t,id:crypto.randomUUID(),timestamp:Date.now()}]})),setLoading:t=>e({isLoading:t}),createNewSession:()=>e({messages:[],currentSession:crypto.randomUUID()})})),Ag="modulepreload",Ng=function(e,t){return new URL(e,t).href},Nu={},p=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){const l=document.getElementsByTagName("link"),s=document.querySelector("meta[property=csp-nonce]"),a=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));o=Promise.allSettled(n.map(u=>{if(u=Ng(u,r),u in Nu)return;Nu[u]=!0;const d=u.endsWith(".css"),f=d?'[rel="stylesheet"]':"";if(!!r)for(let v=l.length-1;v>=0;v--){const E=l[v];if(E.href===u&&(!d||E.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${f}`))return;const g=document.createElement("link");if(g.rel=d?"stylesheet":Ag,d||(g.as="script"),g.crossOrigin="",g.href=u,a&&g.setAttribute("nonce",a),document.head.appendChild(g),d)return new Promise((v,E)=>{g.addEventListener("load",v),g.addEventListener("error",()=>E(new Error(`Unable to preload CSS for ${u}`)))})}))}function i(l){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=l,window.dispatchEvent(s),!s.defaultPrevented)throw l}return o.then(l=>{for(const s of l||[])s.status==="rejected"&&i(s.reason);return t().catch(i)})},xp=[{id:"abap",name:"ABAP",import:()=>p(()=>import("./assets/abap-DsBKuouk.js"),[],import.meta.url)},{id:"actionscript-3",name:"ActionScript",import:()=>p(()=>import("./assets/actionscript-3-D_z4Izcz.js"),[],import.meta.url)},{id:"ada",name:"Ada",import:()=>p(()=>import("./assets/ada-727ZlQH0.js"),[],import.meta.url)},{id:"angular-html",name:"Angular HTML",import:()=>p(()=>import("./assets/angular-html-LfdN0zeE.js").then(e=>e.f),__vite__mapDeps([0,1,2,3]),import.meta.url)},{id:"angular-ts",name:"Angular TypeScript",import:()=>p(()=>import("./assets/angular-ts-CKsD7JZE.js"),__vite__mapDeps([4,0,1,2,3,5]),import.meta.url)},{id:"apache",name:"Apache Conf",import:()=>p(()=>import("./assets/apache-Dn00JSTd.js"),[],import.meta.url)},{id:"apex",name:"Apex",import:()=>p(()=>import("./assets/apex-COJ4H7py.js"),[],import.meta.url)},{id:"apl",name:"APL",import:()=>p(()=>import("./assets/apl-BBq3IX1j.js"),__vite__mapDeps([6,1,2,3,7,8,9]),import.meta.url)},{id:"applescript",name:"AppleScript",import:()=>p(()=>import("./assets/applescript-Bu5BbsvL.js"),[],import.meta.url)},{id:"ara",name:"Ara",import:()=>p(()=>import("./assets/ara-7O62HKoU.js"),[],import.meta.url)},{id:"asciidoc",name:"AsciiDoc",aliases:["adoc"],import:()=>p(()=>import("./assets/asciidoc-BPT9niGB.js"),[],import.meta.url)},{id:"asm",name:"Assembly",import:()=>p(()=>import("./assets/asm-Dhn9LcZ4.js"),[],import.meta.url)},{id:"astro",name:"Astro",import:()=>p(()=>import("./assets/astro-CqkE3fuf.js"),__vite__mapDeps([10,9,2,11,3,12]),import.meta.url)},{id:"awk",name:"AWK",import:()=>p(()=>import("./assets/awk-eg146-Ew.js"),[],import.meta.url)},{id:"ballerina",name:"Ballerina",import:()=>p(()=>import("./assets/ballerina-Du268qiB.js"),[],import.meta.url)},{id:"bat",name:"Batch File",aliases:["batch"],import:()=>p(()=>import("./assets/bat-fje9CFhw.js"),[],import.meta.url)},{id:"beancount",name:"Beancount",import:()=>p(()=>import("./assets/beancount-BwXTMy5W.js"),[],import.meta.url)},{id:"berry",name:"Berry",aliases:["be"],import:()=>p(()=>import("./assets/berry-3xVqZejG.js"),[],import.meta.url)},{id:"bibtex",name:"BibTeX",import:()=>p(()=>import("./assets/bibtex-xW4inM5L.js"),[],import.meta.url)},{id:"bicep",name:"Bicep",import:()=>p(()=>import("./assets/bicep-DHo0CJ0O.js"),[],import.meta.url)},{id:"blade",name:"Blade",import:()=>p(()=>import("./assets/blade-a8OxSdnT.js"),__vite__mapDeps([13,1,2,3,7,8,14,9]),import.meta.url)},{id:"bsl",name:"1C (Enterprise)",aliases:["1c"],import:()=>p(()=>import("./assets/bsl-Dgyn0ogV.js"),__vite__mapDeps([15,16]),import.meta.url)},{id:"c",name:"C",import:()=>p(()=>import("./assets/c-C3t2pwGQ.js"),[],import.meta.url)},{id:"cadence",name:"Cadence",aliases:["cdc"],import:()=>p(()=>import("./assets/cadence-DNquZEk8.js"),[],import.meta.url)},{id:"cairo",name:"Cairo",import:()=>p(()=>import("./assets/cairo--RitsXJZ.js"),__vite__mapDeps([17,18]),import.meta.url)},{id:"clarity",name:"Clarity",import:()=>p(()=>import("./assets/clarity-BHOwM8T6.js"),[],import.meta.url)},{id:"clojure",name:"Clojure",aliases:["clj"],import:()=>p(()=>import("./assets/clojure-DxSadP1t.js"),[],import.meta.url)},{id:"cmake",name:"CMake",import:()=>p(()=>import("./assets/cmake-DbXoA79R.js"),[],import.meta.url)},{id:"cobol",name:"COBOL",import:()=>p(()=>import("./assets/cobol-PTqiYgYu.js"),__vite__mapDeps([19,1,2,3,8]),import.meta.url)},{id:"codeowners",name:"CODEOWNERS",import:()=>p(()=>import("./assets/codeowners-Bp6g37R7.js"),[],import.meta.url)},{id:"codeql",name:"CodeQL",aliases:["ql"],import:()=>p(()=>import("./assets/codeql-sacFqUAJ.js"),[],import.meta.url)},{id:"coffee",name:"CoffeeScript",aliases:["coffeescript"],import:()=>p(()=>import("./assets/coffee-dyiR41kL.js"),__vite__mapDeps([20,2]),import.meta.url)},{id:"common-lisp",name:"Common Lisp",aliases:["lisp"],import:()=>p(()=>import("./assets/common-lisp-C7gG9l05.js"),[],import.meta.url)},{id:"coq",name:"Coq",import:()=>p(()=>import("./assets/coq-Dsg_Bt_b.js"),[],import.meta.url)},{id:"cpp",name:"C++",aliases:["c++"],import:()=>p(()=>import("./assets/cpp-BksuvNSY.js"),__vite__mapDeps([21,22,23,24,14]),import.meta.url)},{id:"crystal",name:"Crystal",import:()=>p(()=>import("./assets/crystal-DtDmRg-F.js"),__vite__mapDeps([25,1,2,3,14,24,26]),import.meta.url)},{id:"csharp",name:"C#",aliases:["c#","cs"],import:()=>p(()=>import("./assets/csharp-D9R-vmeu.js"),[],import.meta.url)},{id:"css",name:"CSS",import:()=>p(()=>import("./assets/css-BPhBrDlE.js"),[],import.meta.url)},{id:"csv",name:"CSV",import:()=>p(()=>import("./assets/csv-B0qRVHPH.js"),[],import.meta.url)},{id:"cue",name:"CUE",import:()=>p(()=>import("./assets/cue-DtFQj3wx.js"),[],import.meta.url)},{id:"cypher",name:"Cypher",aliases:["cql"],import:()=>p(()=>import("./assets/cypher-m2LEI-9-.js"),[],import.meta.url)},{id:"d",name:"D",import:()=>p(()=>import("./assets/d-BoXegm-a.js"),[],import.meta.url)},{id:"dart",name:"Dart",import:()=>p(()=>import("./assets/dart-B9wLZaAG.js"),[],import.meta.url)},{id:"dax",name:"DAX",import:()=>p(()=>import("./assets/dax-ClGRhx96.js"),[],import.meta.url)},{id:"desktop",name:"Desktop",import:()=>p(()=>import("./assets/desktop-DEIpsLCJ.js"),[],import.meta.url)},{id:"diff",name:"Diff",import:()=>p(()=>import("./assets/diff-BgYniUM_.js"),[],import.meta.url)},{id:"docker",name:"Dockerfile",aliases:["dockerfile"],import:()=>p(()=>import("./assets/docker-COcR7UxN.js"),[],import.meta.url)},{id:"dotenv",name:"dotEnv",import:()=>p(()=>import("./assets/dotenv-BjQB5zDj.js"),[],import.meta.url)},{id:"dream-maker",name:"Dream Maker",import:()=>p(()=>import("./assets/dream-maker-C-nORZOA.js"),[],import.meta.url)},{id:"edge",name:"Edge",import:()=>p(()=>import("./assets/edge-D5gP-w-T.js"),__vite__mapDeps([27,11,1,2,3,28]),import.meta.url)},{id:"elixir",name:"Elixir",import:()=>p(()=>import("./assets/elixir-CLiX3zqd.js"),__vite__mapDeps([29,1,2,3]),import.meta.url)},{id:"elm",name:"Elm",import:()=>p(()=>import("./assets/elm-CmHSxxaM.js"),__vite__mapDeps([30,23,24]),import.meta.url)},{id:"emacs-lisp",name:"Emacs Lisp",aliases:["elisp"],import:()=>p(()=>import("./assets/emacs-lisp-BX77sIaO.js"),[],import.meta.url)},{id:"erb",name:"ERB",import:()=>p(()=>import("./assets/erb-BYTLMnw6.js"),__vite__mapDeps([31,1,2,3,32,33,7,8,14,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"erlang",name:"Erlang",aliases:["erl"],import:()=>p(()=>import("./assets/erlang-B-DoSBHF.js"),[],import.meta.url)},{id:"fennel",name:"Fennel",import:()=>p(()=>import("./assets/fennel-bCA53EVm.js"),[],import.meta.url)},{id:"fish",name:"Fish",import:()=>p(()=>import("./assets/fish-w-ucz2PV.js"),[],import.meta.url)},{id:"fluent",name:"Fluent",aliases:["ftl"],import:()=>p(()=>import("./assets/fluent-Dayu4EKP.js"),[],import.meta.url)},{id:"fortran-fixed-form",name:"Fortran (Fixed Form)",aliases:["f","for","f77"],import:()=>p(()=>import("./assets/fortran-fixed-form-TqA4NnZg.js"),__vite__mapDeps([39,40]),import.meta.url)},{id:"fortran-free-form",name:"Fortran (Free Form)",aliases:["f90","f95","f03","f08","f18"],import:()=>p(()=>import("./assets/fortran-free-form-DKXYxT9g.js"),[],import.meta.url)},{id:"fsharp",name:"F#",aliases:["f#","fs"],import:()=>p(()=>import("./assets/fsharp-XplgxFYe.js"),__vite__mapDeps([41,42]),import.meta.url)},{id:"gdresource",name:"GDResource",import:()=>p(()=>import("./assets/gdresource-BHYsBjWJ.js"),__vite__mapDeps([43,44,45]),import.meta.url)},{id:"gdscript",name:"GDScript",import:()=>p(()=>import("./assets/gdscript-DfxzS6Rs.js"),[],import.meta.url)},{id:"gdshader",name:"GDShader",import:()=>p(()=>import("./assets/gdshader-SKMF96pI.js"),[],import.meta.url)},{id:"genie",name:"Genie",import:()=>p(()=>import("./assets/genie-ajMbGru0.js"),[],import.meta.url)},{id:"gherkin",name:"Gherkin",import:()=>p(()=>import("./assets/gherkin--30QC5Em.js"),[],import.meta.url)},{id:"git-commit",name:"Git Commit Message",import:()=>p(()=>import("./assets/git-commit-i4q6IMui.js"),__vite__mapDeps([46,47]),import.meta.url)},{id:"git-rebase",name:"Git Rebase Message",import:()=>p(()=>import("./assets/git-rebase-B-v9cOL2.js"),__vite__mapDeps([48,26]),import.meta.url)},{id:"gleam",name:"Gleam",import:()=>p(()=>import("./assets/gleam-B430Bg39.js"),[],import.meta.url)},{id:"glimmer-js",name:"Glimmer JS",aliases:["gjs"],import:()=>p(()=>import("./assets/glimmer-js-D-cwc0-E.js"),__vite__mapDeps([49,2,11,3,1]),import.meta.url)},{id:"glimmer-ts",name:"Glimmer TS",aliases:["gts"],import:()=>p(()=>import("./assets/glimmer-ts-pgjy16dm.js"),__vite__mapDeps([50,11,3,2,1]),import.meta.url)},{id:"glsl",name:"GLSL",import:()=>p(()=>import("./assets/glsl-DBO2IWDn.js"),__vite__mapDeps([23,24]),import.meta.url)},{id:"gnuplot",name:"Gnuplot",import:()=>p(()=>import("./assets/gnuplot-CM8KxXT1.js"),[],import.meta.url)},{id:"go",name:"Go",import:()=>p(()=>import("./assets/go-B1SYOhNW.js"),[],import.meta.url)},{id:"graphql",name:"GraphQL",aliases:["gql"],import:()=>p(()=>import("./assets/graphql-cDcHW_If.js"),__vite__mapDeps([34,2,11,35,36]),import.meta.url)},{id:"groovy",name:"Groovy",import:()=>p(()=>import("./assets/groovy-DkBy-JyN.js"),[],import.meta.url)},{id:"hack",name:"Hack",import:()=>p(()=>import("./assets/hack-D1yCygmZ.js"),__vite__mapDeps([51,1,2,3,14]),import.meta.url)},{id:"haml",name:"Ruby Haml",import:()=>p(()=>import("./assets/haml-B2EZWmdv.js"),__vite__mapDeps([33,2,3]),import.meta.url)},{id:"handlebars",name:"Handlebars",aliases:["hbs"],import:()=>p(()=>import("./assets/handlebars-BQGss363.js"),__vite__mapDeps([52,1,2,3,38]),import.meta.url)},{id:"haskell",name:"Haskell",aliases:["hs"],import:()=>p(()=>import("./assets/haskell-BILxekzW.js"),[],import.meta.url)},{id:"haxe",name:"Haxe",import:()=>p(()=>import("./assets/haxe-C5wWYbrZ.js"),[],import.meta.url)},{id:"hcl",name:"HashiCorp HCL",import:()=>p(()=>import("./assets/hcl-HzYwdGDm.js"),[],import.meta.url)},{id:"hjson",name:"Hjson",import:()=>p(()=>import("./assets/hjson-T-Tgc4AT.js"),[],import.meta.url)},{id:"hlsl",name:"HLSL",import:()=>p(()=>import("./assets/hlsl-ifBTmRxC.js"),[],import.meta.url)},{id:"html",name:"HTML",import:()=>p(()=>import("./assets/html-C2L_23MC.js"),__vite__mapDeps([1,2,3]),import.meta.url)},{id:"html-derivative",name:"HTML (Derivative)",import:()=>p(()=>import("./assets/html-derivative-CSfWNPLT.js"),__vite__mapDeps([28,1,2,3]),import.meta.url)},{id:"http",name:"HTTP",import:()=>p(()=>import("./assets/http-FRrOvY1W.js"),__vite__mapDeps([53,26,9,7,8,34,2,11,35,36]),import.meta.url)},{id:"hxml",name:"HXML",import:()=>p(()=>import("./assets/hxml-TIA70rKU.js"),__vite__mapDeps([54,55]),import.meta.url)},{id:"hy",name:"Hy",import:()=>p(()=>import("./assets/hy-BMj5Y0dO.js"),[],import.meta.url)},{id:"imba",name:"Imba",import:()=>p(()=>import("./assets/imba-bv_oIlVt.js"),__vite__mapDeps([56,11]),import.meta.url)},{id:"ini",name:"INI",aliases:["properties"],import:()=>p(()=>import("./assets/ini-BjABl1g7.js"),[],import.meta.url)},{id:"java",name:"Java",import:()=>p(()=>import("./assets/java-xI-RfyKK.js"),[],import.meta.url)},{id:"javascript",name:"JavaScript",aliases:["js"],import:()=>p(()=>import("./assets/javascript-ySlJ1b_l.js"),[],import.meta.url)},{id:"jinja",name:"Jinja",import:()=>p(()=>import("./assets/jinja-DGy0s7-h.js"),__vite__mapDeps([57,1,2,3]),import.meta.url)},{id:"jison",name:"Jison",import:()=>p(()=>import("./assets/jison-BqZprYcd.js"),__vite__mapDeps([58,2]),import.meta.url)},{id:"json",name:"JSON",import:()=>p(()=>import("./assets/json-BQoSv7ci.js"),[],import.meta.url)},{id:"json5",name:"JSON5",import:()=>p(()=>import("./assets/json5-w8dY5SsB.js"),[],import.meta.url)},{id:"jsonc",name:"JSON with Comments",import:()=>p(()=>import("./assets/jsonc-TU54ms6u.js"),[],import.meta.url)},{id:"jsonl",name:"JSON Lines",import:()=>p(()=>import("./assets/jsonl-DREVFZK8.js"),[],import.meta.url)},{id:"jsonnet",name:"Jsonnet",import:()=>p(()=>import("./assets/jsonnet-BfivnA6A.js"),[],import.meta.url)},{id:"jssm",name:"JSSM",aliases:["fsl"],import:()=>p(()=>import("./assets/jssm-P4WzXJd0.js"),[],import.meta.url)},{id:"jsx",name:"JSX",import:()=>p(()=>import("./assets/jsx-BAng5TT0.js"),[],import.meta.url)},{id:"julia",name:"Julia",aliases:["jl"],import:()=>p(()=>import("./assets/julia-BBuGR-5E.js"),__vite__mapDeps([59,21,22,23,24,14,18,2,60]),import.meta.url)},{id:"kotlin",name:"Kotlin",aliases:["kt","kts"],import:()=>p(()=>import("./assets/kotlin-B5lbUyaz.js"),[],import.meta.url)},{id:"kusto",name:"Kusto",aliases:["kql"],import:()=>p(()=>import("./assets/kusto-mebxcVVE.js"),[],import.meta.url)},{id:"latex",name:"LaTeX",import:()=>p(()=>import("./assets/latex-C-cWTeAZ.js"),__vite__mapDeps([61,62,60]),import.meta.url)},{id:"lean",name:"Lean 4",aliases:["lean4"],import:()=>p(()=>import("./assets/lean-XBlWyCtg.js"),[],import.meta.url)},{id:"less",name:"Less",import:()=>p(()=>import("./assets/less-BfCpw3nA.js"),[],import.meta.url)},{id:"liquid",name:"Liquid",import:()=>p(()=>import("./assets/liquid-D3W5UaiH.js"),__vite__mapDeps([63,1,2,3,9]),import.meta.url)},{id:"log",name:"Log file",import:()=>p(()=>import("./assets/log-Cc5clBb7.js"),[],import.meta.url)},{id:"logo",name:"Logo",import:()=>p(()=>import("./assets/logo-IuBKFhSY.js"),[],import.meta.url)},{id:"lua",name:"Lua",import:()=>p(()=>import("./assets/lua-CvWAzNxB.js"),__vite__mapDeps([37,24]),import.meta.url)},{id:"luau",name:"Luau",import:()=>p(()=>import("./assets/luau-Du5NY7AG.js"),[],import.meta.url)},{id:"make",name:"Makefile",aliases:["makefile"],import:()=>p(()=>import("./assets/make-Bvotw-X0.js"),[],import.meta.url)},{id:"markdown",name:"Markdown",aliases:["md"],import:()=>p(()=>import("./assets/markdown-UIAJJxZW.js"),[],import.meta.url)},{id:"marko",name:"Marko",import:()=>p(()=>import("./assets/marko-z0MBrx5-.js"),__vite__mapDeps([64,3,65,5,2]),import.meta.url)},{id:"matlab",name:"MATLAB",import:()=>p(()=>import("./assets/matlab-D9-PGadD.js"),[],import.meta.url)},{id:"mdc",name:"MDC",import:()=>p(()=>import("./assets/mdc-DB_EDNY_.js"),__vite__mapDeps([66,42,38,28,1,2,3]),import.meta.url)},{id:"mdx",name:"MDX",import:()=>p(()=>import("./assets/mdx-sdHcTMYB.js"),[],import.meta.url)},{id:"mermaid",name:"Mermaid",aliases:["mmd"],import:()=>p(()=>import("./assets/mermaid-Ci6OQyBP.js"),[],import.meta.url)},{id:"mipsasm",name:"MIPS Assembly",aliases:["mips"],import:()=>p(()=>import("./assets/mipsasm-BC5c_5Pe.js"),[],import.meta.url)},{id:"mojo",name:"Mojo",import:()=>p(()=>import("./assets/mojo-Tz6hzZYG.js"),[],import.meta.url)},{id:"move",name:"Move",import:()=>p(()=>import("./assets/move-DB_GagMm.js"),[],import.meta.url)},{id:"narrat",name:"Narrat Language",aliases:["nar"],import:()=>p(()=>import("./assets/narrat-DLbgOhZU.js"),[],import.meta.url)},{id:"nextflow",name:"Nextflow",aliases:["nf"],import:()=>p(()=>import("./assets/nextflow-B0XVJmRM.js"),[],import.meta.url)},{id:"nginx",name:"Nginx",import:()=>p(()=>import("./assets/nginx-D_VnBJ67.js"),__vite__mapDeps([67,37,24]),import.meta.url)},{id:"nim",name:"Nim",import:()=>p(()=>import("./assets/nim-ZlGxZxc3.js"),__vite__mapDeps([68,24,1,2,3,7,8,23,42]),import.meta.url)},{id:"nix",name:"Nix",import:()=>p(()=>import("./assets/nix-shcSOmrb.js"),[],import.meta.url)},{id:"nushell",name:"nushell",aliases:["nu"],import:()=>p(()=>import("./assets/nushell-D4Tzg5kh.js"),[],import.meta.url)},{id:"objective-c",name:"Objective-C",aliases:["objc"],import:()=>p(()=>import("./assets/objective-c-Deuh7S70.js"),[],import.meta.url)},{id:"objective-cpp",name:"Objective-C++",import:()=>p(()=>import("./assets/objective-cpp-BUEGK8hf.js"),[],import.meta.url)},{id:"ocaml",name:"OCaml",import:()=>p(()=>import("./assets/ocaml-BNioltXt.js"),[],import.meta.url)},{id:"pascal",name:"Pascal",import:()=>p(()=>import("./assets/pascal-JqZropPD.js"),[],import.meta.url)},{id:"perl",name:"Perl",import:()=>p(()=>import("./assets/perl-CHQXSrWU.js"),__vite__mapDeps([69,1,2,3,7,8,14]),import.meta.url)},{id:"php",name:"PHP",import:()=>p(()=>import("./assets/php-B5ebYQev.js"),__vite__mapDeps([70,1,2,3,7,8,14,9]),import.meta.url)},{id:"plsql",name:"PL/SQL",import:()=>p(()=>import("./assets/plsql-LKU2TuZ1.js"),[],import.meta.url)},{id:"po",name:"Gettext PO",aliases:["pot","potx"],import:()=>p(()=>import("./assets/po-BFLt1xDp.js"),[],import.meta.url)},{id:"polar",name:"Polar",import:()=>p(()=>import("./assets/polar-DKykz6zU.js"),[],import.meta.url)},{id:"postcss",name:"PostCSS",import:()=>p(()=>import("./assets/postcss-B3ZDOciz.js"),[],import.meta.url)},{id:"powerquery",name:"PowerQuery",import:()=>p(()=>import("./assets/powerquery-CSHBycmS.js"),[],import.meta.url)},{id:"powershell",name:"PowerShell",aliases:["ps","ps1"],import:()=>p(()=>import("./assets/powershell-BIEUsx6d.js"),[],import.meta.url)},{id:"prisma",name:"Prisma",import:()=>p(()=>import("./assets/prisma-B48N-Iqd.js"),[],import.meta.url)},{id:"prolog",name:"Prolog",import:()=>p(()=>import("./assets/prolog-BY-TUvya.js"),[],import.meta.url)},{id:"proto",name:"Protocol Buffer 3",aliases:["protobuf"],import:()=>p(()=>import("./assets/proto-zocC4JxJ.js"),[],import.meta.url)},{id:"pug",name:"Pug",aliases:["jade"],import:()=>p(()=>import("./assets/pug-CM9l7STV.js"),__vite__mapDeps([71,2,3,1]),import.meta.url)},{id:"puppet",name:"Puppet",import:()=>p(()=>import("./assets/puppet-Cza_XSSt.js"),[],import.meta.url)},{id:"purescript",name:"PureScript",import:()=>p(()=>import("./assets/purescript-Bg-kzb6g.js"),[],import.meta.url)},{id:"python",name:"Python",aliases:["py"],import:()=>p(()=>import("./assets/python-DhUJRlN_.js"),[],import.meta.url)},{id:"qml",name:"QML",import:()=>p(()=>import("./assets/qml-D8XfuvdV.js"),__vite__mapDeps([72,2]),import.meta.url)},{id:"qmldir",name:"QML Directory",import:()=>p(()=>import("./assets/qmldir-C8lEn-DE.js"),[],import.meta.url)},{id:"qss",name:"Qt Style Sheets",import:()=>p(()=>import("./assets/qss-DhMKtDLN.js"),[],import.meta.url)},{id:"r",name:"R",import:()=>p(()=>import("./assets/r-CwjWoCRV.js"),[],import.meta.url)},{id:"racket",name:"Racket",import:()=>p(()=>import("./assets/racket-CzouJOBO.js"),[],import.meta.url)},{id:"raku",name:"Raku",aliases:["perl6"],import:()=>p(()=>import("./assets/raku-B1bQXN8T.js"),[],import.meta.url)},{id:"razor",name:"ASP.NET Razor",import:()=>p(()=>import("./assets/razor-CNLDkMZG.js"),__vite__mapDeps([73,1,2,3,74]),import.meta.url)},{id:"reg",name:"Windows Registry Script",import:()=>p(()=>import("./assets/reg-5LuOXUq_.js"),[],import.meta.url)},{id:"regexp",name:"RegExp",aliases:["regex"],import:()=>p(()=>import("./assets/regexp-DWJ3fJO_.js"),[],import.meta.url)},{id:"rel",name:"Rel",import:()=>p(()=>import("./assets/rel-DJlmqQ1C.js"),[],import.meta.url)},{id:"riscv",name:"RISC-V",import:()=>p(()=>import("./assets/riscv-QhoSD0DR.js"),[],import.meta.url)},{id:"rst",name:"reStructuredText",import:()=>p(()=>import("./assets/rst-4NLicBqY.js"),__vite__mapDeps([75,28,1,2,3,21,22,23,24,14,18,26,38,76,32,33,7,8,34,11,35,36,37]),import.meta.url)},{id:"ruby",name:"Ruby",aliases:["rb"],import:()=>p(()=>import("./assets/ruby-DeZ3UC14.js"),__vite__mapDeps([32,1,2,3,33,7,8,14,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"rust",name:"Rust",aliases:["rs"],import:()=>p(()=>import("./assets/rust-Be6lgOlo.js"),[],import.meta.url)},{id:"sas",name:"SAS",import:()=>p(()=>import("./assets/sas-BmTFh92c.js"),__vite__mapDeps([77,14]),import.meta.url)},{id:"sass",name:"Sass",import:()=>p(()=>import("./assets/sass-BJ4Li9vH.js"),[],import.meta.url)},{id:"scala",name:"Scala",import:()=>p(()=>import("./assets/scala-DQVVAn-B.js"),[],import.meta.url)},{id:"scheme",name:"Scheme",import:()=>p(()=>import("./assets/scheme-BJGe-b2p.js"),[],import.meta.url)},{id:"scss",name:"SCSS",import:()=>p(()=>import("./assets/scss-C31hgJw-.js"),__vite__mapDeps([5,3]),import.meta.url)},{id:"sdbl",name:"1C (Query)",aliases:["1c-query"],import:()=>p(()=>import("./assets/sdbl-BLhTXw86.js"),[],import.meta.url)},{id:"shaderlab",name:"ShaderLab",aliases:["shader"],import:()=>p(()=>import("./assets/shaderlab-B7qAK45m.js"),__vite__mapDeps([78,79]),import.meta.url)},{id:"shellscript",name:"Shell",aliases:["bash","sh","shell","zsh"],import:()=>p(()=>import("./assets/shellscript-atvbtKCR.js"),[],import.meta.url)},{id:"shellsession",name:"Shell Session",aliases:["console"],import:()=>p(()=>import("./assets/shellsession-C_rIy8kc.js"),__vite__mapDeps([80,26]),import.meta.url)},{id:"smalltalk",name:"Smalltalk",import:()=>p(()=>import("./assets/smalltalk-DkLiglaE.js"),[],import.meta.url)},{id:"solidity",name:"Solidity",import:()=>p(()=>import("./assets/solidity-C1w2a3ep.js"),[],import.meta.url)},{id:"soy",name:"Closure Templates",aliases:["closure-templates"],import:()=>p(()=>import("./assets/soy-C-lX7w71.js"),__vite__mapDeps([81,1,2,3]),import.meta.url)},{id:"sparql",name:"SPARQL",import:()=>p(()=>import("./assets/sparql-bYkjHRlG.js"),__vite__mapDeps([82,83]),import.meta.url)},{id:"splunk",name:"Splunk Query Language",aliases:["spl"],import:()=>p(()=>import("./assets/splunk-Cf8iN4DR.js"),[],import.meta.url)},{id:"sql",name:"SQL",import:()=>p(()=>import("./assets/sql-COK4E0Yg.js"),[],import.meta.url)},{id:"ssh-config",name:"SSH Config",import:()=>p(()=>import("./assets/ssh-config-BknIz3MU.js"),[],import.meta.url)},{id:"stata",name:"Stata",import:()=>p(()=>import("./assets/stata-DorPZHa4.js"),__vite__mapDeps([84,14]),import.meta.url)},{id:"stylus",name:"Stylus",aliases:["styl"],import:()=>p(()=>import("./assets/stylus-BeQkCIfX.js"),[],import.meta.url)},{id:"svelte",name:"Svelte",import:()=>p(()=>import("./assets/svelte-MSaWC3Je.js"),__vite__mapDeps([85,2,11,3,12]),import.meta.url)},{id:"swift",name:"Swift",import:()=>p(()=>import("./assets/swift-BSxZ-RaX.js"),[],import.meta.url)},{id:"system-verilog",name:"SystemVerilog",import:()=>p(()=>import("./assets/system-verilog-C7L56vO4.js"),[],import.meta.url)},{id:"systemd",name:"Systemd Units",import:()=>p(()=>import("./assets/systemd-CUnW07Te.js"),[],import.meta.url)},{id:"talonscript",name:"TalonScript",aliases:["talon"],import:()=>p(()=>import("./assets/talonscript-C1XDQQGZ.js"),[],import.meta.url)},{id:"tasl",name:"Tasl",import:()=>p(()=>import("./assets/tasl-CQjiPCtT.js"),[],import.meta.url)},{id:"tcl",name:"Tcl",import:()=>p(()=>import("./assets/tcl-DQ1-QYvQ.js"),[],import.meta.url)},{id:"templ",name:"Templ",import:()=>p(()=>import("./assets/templ-dwX3ZSMB.js"),__vite__mapDeps([86,87,2,3]),import.meta.url)},{id:"terraform",name:"Terraform",aliases:["tf","tfvars"],import:()=>p(()=>import("./assets/terraform-BbSNqyBO.js"),[],import.meta.url)},{id:"tex",name:"TeX",import:()=>p(()=>import("./assets/tex-rYs2v40G.js"),__vite__mapDeps([62,60]),import.meta.url)},{id:"toml",name:"TOML",import:()=>p(()=>import("./assets/toml-CB2ApiWb.js"),[],import.meta.url)},{id:"ts-tags",name:"TypeScript with Tags",aliases:["lit"],import:()=>p(()=>import("./assets/ts-tags-CipyTH0X.js"),__vite__mapDeps([88,11,3,2,23,24,1,14,7,8]),import.meta.url)},{id:"tsv",name:"TSV",import:()=>p(()=>import("./assets/tsv-B_m7g4N7.js"),[],import.meta.url)},{id:"tsx",name:"TSX",import:()=>p(()=>import("./assets/tsx-B6W0miNI.js"),[],import.meta.url)},{id:"turtle",name:"Turtle",import:()=>p(()=>import("./assets/turtle-BMR_PYu6.js"),[],import.meta.url)},{id:"twig",name:"Twig",import:()=>p(()=>import("./assets/twig-NC5TFiHP.js"),__vite__mapDeps([89,3,2,5,70,1,7,8,14,9,18,32,33,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"typescript",name:"TypeScript",aliases:["ts"],import:()=>p(()=>import("./assets/typescript-Dj6nwHGl.js"),[],import.meta.url)},{id:"typespec",name:"TypeSpec",aliases:["tsp"],import:()=>p(()=>import("./assets/typespec-BpWG_bgh.js"),[],import.meta.url)},{id:"typst",name:"Typst",aliases:["typ"],import:()=>p(()=>import("./assets/typst-BVUVsWT6.js"),[],import.meta.url)},{id:"v",name:"V",import:()=>p(()=>import("./assets/v-CAQ2eGtk.js"),[],import.meta.url)},{id:"vala",name:"Vala",import:()=>p(()=>import("./assets/vala-BFOHcciG.js"),[],import.meta.url)},{id:"vb",name:"Visual Basic",aliases:["cmd"],import:()=>p(()=>import("./assets/vb-CdO5JTpU.js"),[],import.meta.url)},{id:"verilog",name:"Verilog",import:()=>p(()=>import("./assets/verilog-CJaU5se_.js"),[],import.meta.url)},{id:"vhdl",name:"VHDL",import:()=>p(()=>import("./assets/vhdl-DYoNaHQp.js"),[],import.meta.url)},{id:"viml",name:"Vim Script",aliases:["vim","vimscript"],import:()=>p(()=>import("./assets/viml-m4uW47V2.js"),[],import.meta.url)},{id:"vue",name:"Vue",import:()=>p(()=>import("./assets/vue-BuYVFjOK.js"),__vite__mapDeps([90,1,2,3,11,9,28]),import.meta.url)},{id:"vue-html",name:"Vue HTML",import:()=>p(()=>import("./assets/vue-html-xdeiXROB.js"),__vite__mapDeps([91,90,1,2,3,11,9,28]),import.meta.url)},{id:"vyper",name:"Vyper",aliases:["vy"],import:()=>p(()=>import("./assets/vyper-nyqBNV6O.js"),[],import.meta.url)},{id:"wasm",name:"WebAssembly",import:()=>p(()=>import("./assets/wasm-C6j12Q_x.js"),[],import.meta.url)},{id:"wenyan",name:"Wenyan",aliases:["文言"],import:()=>p(()=>import("./assets/wenyan-7A4Fjokl.js"),[],import.meta.url)},{id:"wgsl",name:"WGSL",import:()=>p(()=>import("./assets/wgsl-CB0Krxn9.js"),[],import.meta.url)},{id:"wikitext",name:"Wikitext",aliases:["mediawiki","wiki"],import:()=>p(()=>import("./assets/wikitext-DCE3LsBG.js"),[],import.meta.url)},{id:"wolfram",name:"Wolfram",aliases:["wl"],import:()=>p(()=>import("./assets/wolfram-C3FkfJm5.js"),[],import.meta.url)},{id:"xml",name:"XML",import:()=>p(()=>import("./assets/xml-e3z08dGr.js"),__vite__mapDeps([7,8]),import.meta.url)},{id:"xsl",name:"XSL",import:()=>p(()=>import("./assets/xsl-Dd0NUgwM.js"),__vite__mapDeps([92,7,8]),import.meta.url)},{id:"yaml",name:"YAML",aliases:["yml"],import:()=>p(()=>import("./assets/yaml-CVw76BM1.js"),[],import.meta.url)},{id:"zenscript",name:"ZenScript",import:()=>p(()=>import("./assets/zenscript-HnGAYVZD.js"),[],import.meta.url)},{id:"zig",name:"Zig",import:()=>p(()=>import("./assets/zig-BVz_zdnA.js"),[],import.meta.url)}],bg=Object.fromEntries(xp.map(e=>[e.id,e.import])),Og=Object.fromEntries(xp.flatMap(e=>{var t;return((t=e.aliases)==null?void 0:t.map(n=>[n,e.import]))||[]})),Dg={...bg,...Og},jg=[{id:"andromeeda",displayName:"Andromeeda",type:"dark",import:()=>p(()=>import("./assets/andromeeda-C3khCPGq.js"),[],import.meta.url)},{id:"aurora-x",displayName:"Aurora X",type:"dark",import:()=>p(()=>import("./assets/aurora-x-D-2ljcwZ.js"),[],import.meta.url)},{id:"ayu-dark",displayName:"Ayu Dark",type:"dark",import:()=>p(()=>import("./assets/ayu-dark-Cv9koXgw.js"),[],import.meta.url)},{id:"catppuccin-frappe",displayName:"Catppuccin Frappé",type:"dark",import:()=>p(()=>import("./assets/catppuccin-frappe-CD_QflpE.js"),[],import.meta.url)},{id:"catppuccin-latte",displayName:"Catppuccin Latte",type:"light",import:()=>p(()=>import("./assets/catppuccin-latte-DRW-0cLl.js"),[],import.meta.url)},{id:"catppuccin-macchiato",displayName:"Catppuccin Macchiato",type:"dark",import:()=>p(()=>import("./assets/catppuccin-macchiato-C-_shW-Y.js"),[],import.meta.url)},{id:"catppuccin-mocha",displayName:"Catppuccin Mocha",type:"dark",import:()=>p(()=>import("./assets/catppuccin-mocha-LGGdnPYs.js"),[],import.meta.url)},{id:"dark-plus",displayName:"Dark Plus",type:"dark",import:()=>p(()=>import("./assets/dark-plus-C3mMm8J8.js"),[],import.meta.url)},{id:"dracula",displayName:"Dracula Theme",type:"dark",import:()=>p(()=>import("./assets/dracula-BzJJZx-M.js"),[],import.meta.url)},{id:"dracula-soft",displayName:"Dracula Theme Soft",type:"dark",import:()=>p(()=>import("./assets/dracula-soft-BXkSAIEj.js"),[],import.meta.url)},{id:"everforest-dark",displayName:"Everforest Dark",type:"dark",import:()=>p(()=>import("./assets/everforest-dark-BgDCqdQA.js"),[],import.meta.url)},{id:"everforest-light",displayName:"Everforest Light",type:"light",import:()=>p(()=>import("./assets/everforest-light-C8M2exoo.js"),[],import.meta.url)},{id:"github-dark",displayName:"GitHub Dark",type:"dark",import:()=>p(()=>import("./assets/github-dark-DHJKELXO.js"),[],import.meta.url)},{id:"github-dark-default",displayName:"GitHub Dark Default",type:"dark",import:()=>p(()=>import("./assets/github-dark-default-Cuk6v7N8.js"),[],import.meta.url)},{id:"github-dark-dimmed",displayName:"GitHub Dark Dimmed",type:"dark",import:()=>p(()=>import("./assets/github-dark-dimmed-DH5Ifo-i.js"),[],import.meta.url)},{id:"github-dark-high-contrast",displayName:"GitHub Dark High Contrast",type:"dark",import:()=>p(()=>import("./assets/github-dark-high-contrast-E3gJ1_iC.js"),[],import.meta.url)},{id:"github-light",displayName:"GitHub Light",type:"light",import:()=>p(()=>import("./assets/github-light-DAi9KRSo.js"),[],import.meta.url)},{id:"github-light-default",displayName:"GitHub Light Default",type:"light",import:()=>p(()=>import("./assets/github-light-default-D7oLnXFd.js"),[],import.meta.url)},{id:"github-light-high-contrast",displayName:"GitHub Light High Contrast",type:"light",import:()=>p(()=>import("./assets/github-light-high-contrast-BfjtVDDH.js"),[],import.meta.url)},{id:"houston",displayName:"Houston",type:"dark",import:()=>p(()=>import("./assets/houston-DnULxvSX.js"),[],import.meta.url)},{id:"kanagawa-dragon",displayName:"Kanagawa Dragon",type:"dark",import:()=>p(()=>import("./assets/kanagawa-dragon-CkXjmgJE.js"),[],import.meta.url)},{id:"kanagawa-lotus",displayName:"Kanagawa Lotus",type:"light",import:()=>p(()=>import("./assets/kanagawa-lotus-CfQXZHmo.js"),[],import.meta.url)},{id:"kanagawa-wave",displayName:"Kanagawa Wave",type:"dark",import:()=>p(()=>import("./assets/kanagawa-wave-DWedfzmr.js"),[],import.meta.url)},{id:"laserwave",displayName:"LaserWave",type:"dark",import:()=>p(()=>import("./assets/laserwave-DUszq2jm.js"),[],import.meta.url)},{id:"light-plus",displayName:"Light Plus",type:"light",import:()=>p(()=>import("./assets/light-plus-B7mTdjB0.js"),[],import.meta.url)},{id:"material-theme",displayName:"Material Theme",type:"dark",import:()=>p(()=>import("./assets/material-theme-D5KoaKCx.js"),[],import.meta.url)},{id:"material-theme-darker",displayName:"Material Theme Darker",type:"dark",import:()=>p(()=>import("./assets/material-theme-darker-BfHTSMKl.js"),[],import.meta.url)},{id:"material-theme-lighter",displayName:"Material Theme Lighter",type:"light",import:()=>p(()=>import("./assets/material-theme-lighter-B0m2ddpp.js"),[],import.meta.url)},{id:"material-theme-ocean",displayName:"Material Theme Ocean",type:"dark",import:()=>p(()=>import("./assets/material-theme-ocean-CyktbL80.js"),[],import.meta.url)},{id:"material-theme-palenight",displayName:"Material Theme Palenight",type:"dark",import:()=>p(()=>import("./assets/material-theme-palenight-Csfq5Kiy.js"),[],import.meta.url)},{id:"min-dark",displayName:"Min Dark",type:"dark",import:()=>p(()=>import("./assets/min-dark-CafNBF8u.js"),[],import.meta.url)},{id:"min-light",displayName:"Min Light",type:"light",import:()=>p(()=>import("./assets/min-light-CTRr51gU.js"),[],import.meta.url)},{id:"monokai",displayName:"Monokai",type:"dark",import:()=>p(()=>import("./assets/monokai-D4h5O-jR.js"),[],import.meta.url)},{id:"night-owl",displayName:"Night Owl",type:"dark",import:()=>p(()=>import("./assets/night-owl-C39BiMTA.js"),[],import.meta.url)},{id:"nord",displayName:"Nord",type:"dark",import:()=>p(()=>import("./assets/nord-Ddv68eIx.js"),[],import.meta.url)},{id:"one-dark-pro",displayName:"One Dark Pro",type:"dark",import:()=>p(()=>import("./assets/one-dark-pro-GBQ2dnAY.js"),[],import.meta.url)},{id:"one-light",displayName:"One Light",type:"light",import:()=>p(()=>import("./assets/one-light-PoHY5YXO.js"),[],import.meta.url)},{id:"plastic",displayName:"Plastic",type:"dark",import:()=>p(()=>import("./assets/plastic-3e1v2bzS.js"),[],import.meta.url)},{id:"poimandres",displayName:"Poimandres",type:"dark",import:()=>p(()=>import("./assets/poimandres-CS3Unz2-.js"),[],import.meta.url)},{id:"red",displayName:"Red",type:"dark",import:()=>p(()=>import("./assets/red-bN70gL4F.js"),[],import.meta.url)},{id:"rose-pine",displayName:"Rosé Pine",type:"dark",import:()=>p(()=>import("./assets/rose-pine-CmCqftbK.js"),[],import.meta.url)},{id:"rose-pine-dawn",displayName:"Rosé Pine Dawn",type:"light",import:()=>p(()=>import("./assets/rose-pine-dawn-Ds-gbosJ.js"),[],import.meta.url)},{id:"rose-pine-moon",displayName:"Rosé Pine Moon",type:"dark",import:()=>p(()=>import("./assets/rose-pine-moon-CjDtw9vr.js"),[],import.meta.url)},{id:"slack-dark",displayName:"Slack Dark",type:"dark",import:()=>p(()=>import("./assets/slack-dark-BthQWCQV.js"),[],import.meta.url)},{id:"slack-ochin",displayName:"Slack Ochin",type:"light",import:()=>p(()=>import("./assets/slack-ochin-DqwNpetd.js"),[],import.meta.url)},{id:"snazzy-light",displayName:"Snazzy Light",type:"light",import:()=>p(()=>import("./assets/snazzy-light-Bw305WKR.js"),[],import.meta.url)},{id:"solarized-dark",displayName:"Solarized Dark",type:"dark",import:()=>p(()=>import("./assets/solarized-dark-DXbdFlpD.js"),[],import.meta.url)},{id:"solarized-light",displayName:"Solarized Light",type:"light",import:()=>p(()=>import("./assets/solarized-light-L9t79GZl.js"),[],import.meta.url)},{id:"synthwave-84",displayName:"Synthwave '84",type:"dark",import:()=>p(()=>import("./assets/synthwave-84-CbfX1IO0.js"),[],import.meta.url)},{id:"tokyo-night",displayName:"Tokyo Night",type:"dark",import:()=>p(()=>import("./assets/tokyo-night-DBQeEorK.js"),[],import.meta.url)},{id:"vesper",displayName:"Vesper",type:"dark",import:()=>p(()=>import("./assets/vesper-BEBZ7ncR.js"),[],import.meta.url)},{id:"vitesse-black",displayName:"Vitesse Black",type:"dark",import:()=>p(()=>import("./assets/vitesse-black-Bkuqu6BP.js"),[],import.meta.url)},{id:"vitesse-dark",displayName:"Vitesse Dark",type:"dark",import:()=>p(()=>import("./assets/vitesse-dark-D0r3Knsf.js"),[],import.meta.url)},{id:"vitesse-light",displayName:"Vitesse Light",type:"light",import:()=>p(()=>import("./assets/vitesse-light-CVO1_9PV.js"),[],import.meta.url)}],Mg=Object.fromEntries(jg.map(e=>[e.id,e.import]));let ft=class extends Error{constructor(t){super(t),this.name="ShikiError"}},da=class extends Error{constructor(t){super(t),this.name="ShikiError"}};function Vg(){return 2147483648}function zg(){return typeof performance<"u"?performance.now():Date.now()}const Bg=(e,t)=>e+(t-e%t)%t;async function $g(e){let t,n;const r={};function o(g){n=g,r.HEAPU8=new Uint8Array(g),r.HEAPU32=new Uint32Array(g)}function i(g,v,E){r.HEAPU8.copyWithin(g,v,v+E)}function l(g){try{return t.grow(g-n.byteLength+65535>>>16),o(t.buffer),1}catch{}}function s(g){const v=r.HEAPU8.length;g=g>>>0;const E=Vg();if(g>E)return!1;for(let w=1;w<=4;w*=2){let h=v*(1+.2/w);h=Math.min(h,g+100663296);const c=Math.min(E,Bg(Math.max(g,h),65536));if(l(c))return!0}return!1}const a=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function u(g,v,E=1024){const w=v+E;let h=v;for(;g[h]&&!(h>=w);)++h;if(h-v>16&&g.buffer&&a)return a.decode(g.subarray(v,h));let c="";for(;v<h;){let _=g[v++];if(!(_&128)){c+=String.fromCharCode(_);continue}const S=g[v++]&63;if((_&224)===192){c+=String.fromCharCode((_&31)<<6|S);continue}const C=g[v++]&63;if((_&240)===224?_=(_&15)<<12|S<<6|C:_=(_&7)<<18|S<<12|C<<6|g[v++]&63,_<65536)c+=String.fromCharCode(_);else{const L=_-65536;c+=String.fromCharCode(55296|L>>10,56320|L&1023)}}return c}function d(g,v){return g?u(r.HEAPU8,g,v):""}const f={emscripten_get_now:zg,emscripten_memcpy_big:i,emscripten_resize_heap:s,fd_write:()=>0};async function m(){const v=await e({env:f,wasi_snapshot_preview1:f});t=v.memory,o(t.buffer),Object.assign(r,v),r.UTF8ToString=d}return await m(),r}var Ug=Object.defineProperty,Fg=(e,t,n)=>t in e?Ug(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,te=(e,t,n)=>(Fg(e,typeof t!="symbol"?t+"":t,n),n);let ie=null;function Gg(e){throw new da(e.UTF8ToString(e.getLastOnigError()))}class Ci{constructor(t){te(this,"utf16Length"),te(this,"utf8Length"),te(this,"utf16Value"),te(this,"utf8Value"),te(this,"utf16OffsetToUtf8"),te(this,"utf8OffsetToUtf16");const n=t.length,r=Ci._utf8ByteLength(t),o=r!==n,i=o?new Uint32Array(n+1):null;o&&(i[n]=r);const l=o?new Uint32Array(r+1):null;o&&(l[r]=n);const s=new Uint8Array(r);let a=0;for(let u=0;u<n;u++){const d=t.charCodeAt(u);let f=d,m=!1;if(d>=55296&&d<=56319&&u+1<n){const g=t.charCodeAt(u+1);g>=56320&&g<=57343&&(f=(d-55296<<10)+65536|g-56320,m=!0)}o&&(i[u]=a,m&&(i[u+1]=a),f<=127?l[a+0]=u:f<=2047?(l[a+0]=u,l[a+1]=u):f<=65535?(l[a+0]=u,l[a+1]=u,l[a+2]=u):(l[a+0]=u,l[a+1]=u,l[a+2]=u,l[a+3]=u)),f<=127?s[a++]=f:f<=2047?(s[a++]=192|(f&1984)>>>6,s[a++]=128|(f&63)>>>0):f<=65535?(s[a++]=224|(f&61440)>>>12,s[a++]=128|(f&4032)>>>6,s[a++]=128|(f&63)>>>0):(s[a++]=240|(f&1835008)>>>18,s[a++]=128|(f&258048)>>>12,s[a++]=128|(f&4032)>>>6,s[a++]=128|(f&63)>>>0),m&&u++}this.utf16Length=n,this.utf8Length=r,this.utf16Value=t,this.utf8Value=s,this.utf16OffsetToUtf8=i,this.utf8OffsetToUtf16=l}static _utf8ByteLength(t){let n=0;for(let r=0,o=t.length;r<o;r++){const i=t.charCodeAt(r);let l=i,s=!1;if(i>=55296&&i<=56319&&r+1<o){const a=t.charCodeAt(r+1);a>=56320&&a<=57343&&(l=(i-55296<<10)+65536|a-56320,s=!0)}l<=127?n+=1:l<=2047?n+=2:l<=65535?n+=3:n+=4,s&&r++}return n}createString(t){const n=t.omalloc(this.utf8Length);return t.HEAPU8.set(this.utf8Value,n),n}}const Ze=class{constructor(e){if(te(this,"id",++Ze.LAST_ID),te(this,"_onigBinding"),te(this,"content"),te(this,"utf16Length"),te(this,"utf8Length"),te(this,"utf16OffsetToUtf8"),te(this,"utf8OffsetToUtf16"),te(this,"ptr"),!ie)throw new da("Must invoke loadWasm first.");this._onigBinding=ie,this.content=e;const t=new Ci(e);this.utf16Length=t.utf16Length,this.utf8Length=t.utf8Length,this.utf16OffsetToUtf8=t.utf16OffsetToUtf8,this.utf8OffsetToUtf16=t.utf8OffsetToUtf16,this.utf8Length<1e4&&!Ze._sharedPtrInUse?(Ze._sharedPtr||(Ze._sharedPtr=ie.omalloc(1e4)),Ze._sharedPtrInUse=!0,ie.HEAPU8.set(t.utf8Value,Ze._sharedPtr),this.ptr=Ze._sharedPtr):this.ptr=t.createString(ie)}convertUtf8OffsetToUtf16(e){return this.utf8OffsetToUtf16?e<0?0:e>this.utf8Length?this.utf16Length:this.utf8OffsetToUtf16[e]:e}convertUtf16OffsetToUtf8(e){return this.utf16OffsetToUtf8?e<0?0:e>this.utf16Length?this.utf8Length:this.utf16OffsetToUtf8[e]:e}dispose(){this.ptr===Ze._sharedPtr?Ze._sharedPtrInUse=!1:this._onigBinding.ofree(this.ptr)}};let $r=Ze;te($r,"LAST_ID",0);te($r,"_sharedPtr",0);te($r,"_sharedPtrInUse",!1);class Hg{constructor(t){if(te(this,"_onigBinding"),te(this,"_ptr"),!ie)throw new da("Must invoke loadWasm first.");const n=[],r=[];for(let s=0,a=t.length;s<a;s++){const u=new Ci(t[s]);n[s]=u.createString(ie),r[s]=u.utf8Length}const o=ie.omalloc(4*t.length);ie.HEAPU32.set(n,o/4);const i=ie.omalloc(4*t.length);ie.HEAPU32.set(r,i/4);const l=ie.createOnigScanner(o,i,t.length);for(let s=0,a=t.length;s<a;s++)ie.ofree(n[s]);ie.ofree(i),ie.ofree(o),l===0&&Gg(ie),this._onigBinding=ie,this._ptr=l}dispose(){this._onigBinding.freeOnigScanner(this._ptr)}findNextMatchSync(t,n,r){let o=0;if(typeof r=="number"&&(o=r),typeof t=="string"){t=new $r(t);const i=this._findNextMatchSync(t,n,!1,o);return t.dispose(),i}return this._findNextMatchSync(t,n,!1,o)}_findNextMatchSync(t,n,r,o){const i=this._onigBinding,l=i.findNextOnigScannerMatch(this._ptr,t.id,t.ptr,t.utf8Length,t.convertUtf16OffsetToUtf8(n),o);if(l===0)return null;const s=i.HEAPU32;let a=l/4;const u=s[a++],d=s[a++],f=[];for(let m=0;m<d;m++){const g=t.convertUtf8OffsetToUtf16(s[a++]),v=t.convertUtf8OffsetToUtf16(s[a++]);f[m]={start:g,end:v,length:v-g}}return{index:u,captureIndices:f}}}function Wg(e){return typeof e.instantiator=="function"}function Kg(e){return typeof e.default=="function"}function Qg(e){return typeof e.data<"u"}function qg(e){return typeof Response<"u"&&e instanceof Response}function Yg(e){var t;return typeof ArrayBuffer<"u"&&(e instanceof ArrayBuffer||ArrayBuffer.isView(e))||typeof Buffer<"u"&&((t=Buffer.isBuffer)==null?void 0:t.call(Buffer,e))||typeof SharedArrayBuffer<"u"&&e instanceof SharedArrayBuffer||typeof Uint32Array<"u"&&e instanceof Uint32Array}let uo;function Xg(e){if(uo)return uo;async function t(){ie=await $g(async n=>{let r=e;return r=await r,typeof r=="function"&&(r=await r(n)),typeof r=="function"&&(r=await r(n)),Wg(r)?r=await r.instantiator(n):Kg(r)?r=await r.default(n):(Qg(r)&&(r=r.data),qg(r)?typeof WebAssembly.instantiateStreaming=="function"?r=await Jg(r)(n):r=await Zg(r)(n):Yg(r)?r=await rl(r)(n):r instanceof WebAssembly.Module?r=await rl(r)(n):"default"in r&&r.default instanceof WebAssembly.Module&&(r=await rl(r.default)(n))),"instance"in r&&(r=r.instance),"exports"in r&&(r=r.exports),r})}return uo=t(),uo}function rl(e){return t=>WebAssembly.instantiate(e,t)}function Jg(e){return t=>WebAssembly.instantiateStreaming(e,t)}function Zg(e){return async t=>{const n=await e.arrayBuffer();return WebAssembly.instantiate(n,t)}}let e_;function t_(){return e_}async function kp(e){return e&&await Xg(e),{createScanner(t){return new Hg(t.map(n=>typeof n=="string"?n:n.source))},createString(t){return new $r(t)}}}function n_(e){return pa(e)}function pa(e){return Array.isArray(e)?r_(e):e instanceof RegExp?e:typeof e=="object"?o_(e):e}function r_(e){let t=[];for(let n=0,r=e.length;n<r;n++)t[n]=pa(e[n]);return t}function o_(e){let t={};for(let n in e)t[n]=pa(e[n]);return t}function Cp(e,...t){return t.forEach(n=>{for(let r in n)e[r]=n[r]}),e}function Pp(e){const t=~e.lastIndexOf("/")||~e.lastIndexOf("\\");return t===0?e:~t===e.length-1?Pp(e.substring(0,e.length-1)):e.substr(~t+1)}var ol=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g,co=class{static hasCaptures(e){return e===null?!1:(ol.lastIndex=0,ol.test(e))}static replaceCaptures(e,t,n){return e.replace(ol,(r,o,i,l)=>{let s=n[parseInt(o||i,10)];if(s){let a=t.substring(s.start,s.end);for(;a[0]===".";)a=a.substring(1);switch(l){case"downcase":return a.toLowerCase();case"upcase":return a.toUpperCase();default:return a}}else return r})}};function Rp(e,t){return e<t?-1:e>t?1:0}function Lp(e,t){if(e===null&&t===null)return 0;if(!e)return-1;if(!t)return 1;let n=e.length,r=t.length;if(n===r){for(let o=0;o<n;o++){let i=Rp(e[o],t[o]);if(i!==0)return i}return 0}return n-r}function bu(e){return!!(/^#[0-9a-f]{6}$/i.test(e)||/^#[0-9a-f]{8}$/i.test(e)||/^#[0-9a-f]{3}$/i.test(e)||/^#[0-9a-f]{4}$/i.test(e))}function Tp(e){return e.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")}var Ip=class{constructor(e){x(this,"cache",new Map);this.fn=e}get(e){if(this.cache.has(e))return this.cache.get(e);const t=this.fn(e);return this.cache.set(e,t),t}},Jo=class{constructor(e,t,n){x(this,"_cachedMatchRoot",new Ip(e=>this._root.match(e)));this._colorMap=e,this._defaults=t,this._root=n}static createFromRawTheme(e,t){return this.createFromParsedTheme(s_(e),t)}static createFromParsedTheme(e,t){return u_(e,t)}getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(e){if(e===null)return this._defaults;const t=e.scopeName,r=this._cachedMatchRoot.get(t).find(o=>i_(e.parent,o.parentScopes));return r?new Ap(r.fontStyle,r.foreground,r.background):null}},il=class Po{constructor(t,n){this.parent=t,this.scopeName=n}static push(t,n){for(const r of n)t=new Po(t,r);return t}static from(...t){let n=null;for(let r=0;r<t.length;r++)n=new Po(n,t[r]);return n}push(t){return new Po(this,t)}getSegments(){let t=this;const n=[];for(;t;)n.push(t.scopeName),t=t.parent;return n.reverse(),n}toString(){return this.getSegments().join(" ")}extends(t){return this===t?!0:this.parent===null?!1:this.parent.extends(t)}getExtensionIfDefined(t){const n=[];let r=this;for(;r&&r!==t;)n.push(r.scopeName),r=r.parent;return r===t?n.reverse():void 0}};function i_(e,t){if(t.length===0)return!0;for(let n=0;n<t.length;n++){let r=t[n],o=!1;if(r===">"){if(n===t.length-1)return!1;r=t[++n],o=!0}for(;e&&!l_(e.scopeName,r);){if(o)return!1;e=e.parent}if(!e)return!1;e=e.parent}return!0}function l_(e,t){return t===e||e.startsWith(t)&&e[t.length]==="."}var Ap=class{constructor(e,t,n){this.fontStyle=e,this.foregroundId=t,this.backgroundId=n}};function s_(e){if(!e)return[];if(!e.settings||!Array.isArray(e.settings))return[];let t=e.settings,n=[],r=0;for(let o=0,i=t.length;o<i;o++){let l=t[o];if(!l.settings)continue;let s;if(typeof l.scope=="string"){let f=l.scope;f=f.replace(/^[,]+/,""),f=f.replace(/[,]+$/,""),s=f.split(",")}else Array.isArray(l.scope)?s=l.scope:s=[""];let a=-1;if(typeof l.settings.fontStyle=="string"){a=0;let f=l.settings.fontStyle.split(" ");for(let m=0,g=f.length;m<g;m++)switch(f[m]){case"italic":a=a|1;break;case"bold":a=a|2;break;case"underline":a=a|4;break;case"strikethrough":a=a|8;break}}let u=null;typeof l.settings.foreground=="string"&&bu(l.settings.foreground)&&(u=l.settings.foreground);let d=null;typeof l.settings.background=="string"&&bu(l.settings.background)&&(d=l.settings.background);for(let f=0,m=s.length;f<m;f++){let v=s[f].trim().split(" "),E=v[v.length-1],w=null;v.length>1&&(w=v.slice(0,v.length-1),w.reverse()),n[r++]=new a_(E,w,o,a,u,d)}}return n}var a_=class{constructor(e,t,n,r,o,i){this.scope=e,this.parentScopes=t,this.index=n,this.fontStyle=r,this.foreground=o,this.background=i}},dt=(e=>(e[e.NotSet=-1]="NotSet",e[e.None=0]="None",e[e.Italic=1]="Italic",e[e.Bold=2]="Bold",e[e.Underline=4]="Underline",e[e.Strikethrough=8]="Strikethrough",e))(dt||{});function u_(e,t){e.sort((a,u)=>{let d=Rp(a.scope,u.scope);return d!==0||(d=Lp(a.parentScopes,u.parentScopes),d!==0)?d:a.index-u.index});let n=0,r="#000000",o="#ffffff";for(;e.length>=1&&e[0].scope==="";){let a=e.shift();a.fontStyle!==-1&&(n=a.fontStyle),a.foreground!==null&&(r=a.foreground),a.background!==null&&(o=a.background)}let i=new c_(t),l=new Ap(n,i.getId(r),i.getId(o)),s=new p_(new os(0,null,-1,0,0),[]);for(let a=0,u=e.length;a<u;a++){let d=e[a];s.insert(0,d.scope,d.parentScopes,d.fontStyle,i.getId(d.foreground),i.getId(d.background))}return new Jo(i,l,s)}var c_=class{constructor(e){x(this,"_isFrozen");x(this,"_lastColorId");x(this,"_id2color");x(this,"_color2id");if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(e)){this._isFrozen=!0;for(let t=0,n=e.length;t<n;t++)this._color2id[e[t]]=t,this._id2color[t]=e[t]}else this._isFrozen=!1}getId(e){if(e===null)return 0;e=e.toUpperCase();let t=this._color2id[e];if(t)return t;if(this._isFrozen)throw new Error(`Missing color in color map - ${e}`);return t=++this._lastColorId,this._color2id[e]=t,this._id2color[t]=e,t}getColorMap(){return this._id2color.slice(0)}},d_=Object.freeze([]),os=class Np{constructor(t,n,r,o,i){x(this,"scopeDepth");x(this,"parentScopes");x(this,"fontStyle");x(this,"foreground");x(this,"background");this.scopeDepth=t,this.parentScopes=n||d_,this.fontStyle=r,this.foreground=o,this.background=i}clone(){return new Np(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(t){let n=[];for(let r=0,o=t.length;r<o;r++)n[r]=t[r].clone();return n}acceptOverwrite(t,n,r,o){this.scopeDepth>t?console.log("how did this happen?"):this.scopeDepth=t,n!==-1&&(this.fontStyle=n),r!==0&&(this.foreground=r),o!==0&&(this.background=o)}},p_=class is{constructor(t,n=[],r={}){x(this,"_rulesWithParentScopes");this._mainRule=t,this._children=r,this._rulesWithParentScopes=n}static _cmpBySpecificity(t,n){if(t.scopeDepth!==n.scopeDepth)return n.scopeDepth-t.scopeDepth;let r=0,o=0;for(;t.parentScopes[r]===">"&&r++,n.parentScopes[o]===">"&&o++,!(r>=t.parentScopes.length||o>=n.parentScopes.length);){const i=n.parentScopes[o].length-t.parentScopes[r].length;if(i!==0)return i;r++,o++}return n.parentScopes.length-t.parentScopes.length}match(t){if(t!==""){let r=t.indexOf("."),o,i;if(r===-1?(o=t,i=""):(o=t.substring(0,r),i=t.substring(r+1)),this._children.hasOwnProperty(o))return this._children[o].match(i)}const n=this._rulesWithParentScopes.concat(this._mainRule);return n.sort(is._cmpBySpecificity),n}insert(t,n,r,o,i,l){if(n===""){this._doInsertHere(t,r,o,i,l);return}let s=n.indexOf("."),a,u;s===-1?(a=n,u=""):(a=n.substring(0,s),u=n.substring(s+1));let d;this._children.hasOwnProperty(a)?d=this._children[a]:(d=new is(this._mainRule.clone(),os.cloneArr(this._rulesWithParentScopes)),this._children[a]=d),d.insert(t+1,u,r,o,i,l)}_doInsertHere(t,n,r,o,i){if(n===null){this._mainRule.acceptOverwrite(t,r,o,i);return}for(let l=0,s=this._rulesWithParentScopes.length;l<s;l++){let a=this._rulesWithParentScopes[l];if(Lp(a.parentScopes,n)===0){a.acceptOverwrite(t,r,o,i);return}}r===-1&&(r=this._mainRule.fontStyle),o===0&&(o=this._mainRule.foreground),i===0&&(i=this._mainRule.background),this._rulesWithParentScopes.push(new os(t,n,r,o,i))}},Vn=class Me{static toBinaryStr(t){return t.toString(2).padStart(32,"0")}static print(t){const n=Me.getLanguageId(t),r=Me.getTokenType(t),o=Me.getFontStyle(t),i=Me.getForeground(t),l=Me.getBackground(t);console.log({languageId:n,tokenType:r,fontStyle:o,foreground:i,background:l})}static getLanguageId(t){return(t&255)>>>0}static getTokenType(t){return(t&768)>>>8}static containsBalancedBrackets(t){return(t&1024)!==0}static getFontStyle(t){return(t&30720)>>>11}static getForeground(t){return(t&16744448)>>>15}static getBackground(t){return(t&4278190080)>>>24}static set(t,n,r,o,i,l,s){let a=Me.getLanguageId(t),u=Me.getTokenType(t),d=Me.containsBalancedBrackets(t)?1:0,f=Me.getFontStyle(t),m=Me.getForeground(t),g=Me.getBackground(t);return n!==0&&(a=n),r!==8&&(u=r),o!==null&&(d=o?1:0),i!==-1&&(f=i),l!==0&&(m=l),s!==0&&(g=s),(a<<0|u<<8|d<<10|f<<11|m<<15|g<<24)>>>0}};function Zo(e,t){const n=[],r=f_(e);let o=r.next();for(;o!==null;){let a=0;if(o.length===2&&o.charAt(1)===":"){switch(o.charAt(0)){case"R":a=1;break;case"L":a=-1;break;default:console.log(`Unknown priority ${o} in scope selector`)}o=r.next()}let u=l();if(n.push({matcher:u,priority:a}),o!==",")break;o=r.next()}return n;function i(){if(o==="-"){o=r.next();const a=i();return u=>!!a&&!a(u)}if(o==="("){o=r.next();const a=s();return o===")"&&(o=r.next()),a}if(Ou(o)){const a=[];do a.push(o),o=r.next();while(Ou(o));return u=>t(a,u)}return null}function l(){const a=[];let u=i();for(;u;)a.push(u),u=i();return d=>a.every(f=>f(d))}function s(){const a=[];let u=l();for(;u&&(a.push(u),o==="|"||o===",");){do o=r.next();while(o==="|"||o===",");u=l()}return d=>a.some(f=>f(d))}}function Ou(e){return!!e&&!!e.match(/[\w\.:]+/)}function f_(e){let t=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g,n=t.exec(e);return{next:()=>{if(!n)return null;const r=n[0];return n=t.exec(e),r}}}function bp(e){typeof e.dispose=="function"&&e.dispose()}var Nr=class{constructor(e){this.scopeName=e}toKey(){return this.scopeName}},m_=class{constructor(e,t){this.scopeName=e,this.ruleName=t}toKey(){return`${this.scopeName}#${this.ruleName}`}},h_=class{constructor(){x(this,"_references",[]);x(this,"_seenReferenceKeys",new Set);x(this,"visitedRule",new Set)}get references(){return this._references}add(e){const t=e.toKey();this._seenReferenceKeys.has(t)||(this._seenReferenceKeys.add(t),this._references.push(e))}},g_=class{constructor(e,t){x(this,"seenFullScopeRequests",new Set);x(this,"seenPartialScopeRequests",new Set);x(this,"Q");this.repo=e,this.initialScopeName=t,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new Nr(this.initialScopeName)]}processQueue(){const e=this.Q;this.Q=[];const t=new h_;for(const n of e)__(n,this.initialScopeName,this.repo,t);for(const n of t.references)if(n instanceof Nr){if(this.seenFullScopeRequests.has(n.scopeName))continue;this.seenFullScopeRequests.add(n.scopeName),this.Q.push(n)}else{if(this.seenFullScopeRequests.has(n.scopeName)||this.seenPartialScopeRequests.has(n.toKey()))continue;this.seenPartialScopeRequests.add(n.toKey()),this.Q.push(n)}}};function __(e,t,n,r){const o=n.lookup(e.scopeName);if(!o){if(e.scopeName===t)throw new Error(`No grammar provided for <${t}>`);return}const i=n.lookup(t);e instanceof Nr?Ro({baseGrammar:i,selfGrammar:o},r):ls(e.ruleName,{baseGrammar:i,selfGrammar:o,repository:o.repository},r);const l=n.injections(e.scopeName);if(l)for(const s of l)r.add(new Nr(s))}function ls(e,t,n){if(t.repository&&t.repository[e]){const r=t.repository[e];ei([r],t,n)}}function Ro(e,t){e.selfGrammar.patterns&&Array.isArray(e.selfGrammar.patterns)&&ei(e.selfGrammar.patterns,{...e,repository:e.selfGrammar.repository},t),e.selfGrammar.injections&&ei(Object.values(e.selfGrammar.injections),{...e,repository:e.selfGrammar.repository},t)}function ei(e,t,n){for(const r of e){if(n.visitedRule.has(r))continue;n.visitedRule.add(r);const o=r.repository?Cp({},t.repository,r.repository):t.repository;Array.isArray(r.patterns)&&ei(r.patterns,{...t,repository:o},n);const i=r.include;if(!i)continue;const l=Op(i);switch(l.kind){case 0:Ro({...t,selfGrammar:t.baseGrammar},n);break;case 1:Ro(t,n);break;case 2:ls(l.ruleName,{...t,repository:o},n);break;case 3:case 4:const s=l.scopeName===t.selfGrammar.scopeName?t.selfGrammar:l.scopeName===t.baseGrammar.scopeName?t.baseGrammar:void 0;if(s){const a={baseGrammar:t.baseGrammar,selfGrammar:s,repository:o};l.kind===4?ls(l.ruleName,a,n):Ro(a,n)}else l.kind===4?n.add(new m_(l.scopeName,l.ruleName)):n.add(new Nr(l.scopeName));break}}}var y_=class{constructor(){x(this,"kind",0)}},v_=class{constructor(){x(this,"kind",1)}},E_=class{constructor(e){x(this,"kind",2);this.ruleName=e}},S_=class{constructor(e){x(this,"kind",3);this.scopeName=e}},w_=class{constructor(e,t){x(this,"kind",4);this.scopeName=e,this.ruleName=t}};function Op(e){if(e==="$base")return new y_;if(e==="$self")return new v_;const t=e.indexOf("#");if(t===-1)return new S_(e);if(t===0)return new E_(e.substring(1));{const n=e.substring(0,t),r=e.substring(t+1);return new w_(n,r)}}var x_=/\\(\d+)/,Du=/\\(\d+)/g,k_=-1,Dp=-2;var Ur=class{constructor(e,t,n,r){x(this,"$location");x(this,"id");x(this,"_nameIsCapturing");x(this,"_name");x(this,"_contentNameIsCapturing");x(this,"_contentName");this.$location=e,this.id=t,this._name=n||null,this._nameIsCapturing=co.hasCaptures(this._name),this._contentName=r||null,this._contentNameIsCapturing=co.hasCaptures(this._contentName)}get debugName(){const e=this.$location?`${Pp(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${e}`}getName(e,t){return!this._nameIsCapturing||this._name===null||e===null||t===null?this._name:co.replaceCaptures(this._name,e,t)}getContentName(e,t){return!this._contentNameIsCapturing||this._contentName===null?this._contentName:co.replaceCaptures(this._contentName,e,t)}},C_=class extends Ur{constructor(t,n,r,o,i){super(t,n,r,o);x(this,"retokenizeCapturedWithRuleId");this.retokenizeCapturedWithRuleId=i}dispose(){}collectPatterns(t,n){throw new Error("Not supported!")}compile(t,n){throw new Error("Not supported!")}compileAG(t,n,r,o){throw new Error("Not supported!")}},P_=class extends Ur{constructor(t,n,r,o,i){super(t,n,r,null);x(this,"_match");x(this,"captures");x(this,"_cachedCompiledPatterns");this._match=new br(o,this.id),this.captures=i,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(t,n){n.push(this._match)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new Or,this.collectPatterns(t,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},ju=class extends Ur{constructor(t,n,r,o,i){super(t,n,r,o);x(this,"hasMissingPatterns");x(this,"patterns");x(this,"_cachedCompiledPatterns");this.patterns=i.patterns,this.hasMissingPatterns=i.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(t,n){for(const r of this.patterns)t.getRule(r).collectPatterns(t,n)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new Or,this.collectPatterns(t,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},ss=class extends Ur{constructor(t,n,r,o,i,l,s,a,u,d){super(t,n,r,o);x(this,"_begin");x(this,"beginCaptures");x(this,"_end");x(this,"endHasBackReferences");x(this,"endCaptures");x(this,"applyEndPatternLast");x(this,"hasMissingPatterns");x(this,"patterns");x(this,"_cachedCompiledPatterns");this._begin=new br(i,this.id),this.beginCaptures=l,this._end=new br(s||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=a,this.applyEndPatternLast=u||!1,this.patterns=d.patterns,this.hasMissingPatterns=d.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(t,n){return this._end.resolveBackReferences(t,n)}collectPatterns(t,n){n.push(this._begin)}compile(t,n){return this._getCachedCompiledPatterns(t,n).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t,n).compileAG(t,r,o)}_getCachedCompiledPatterns(t,n){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new Or;for(const r of this.patterns)t.getRule(r).collectPatterns(t,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,n):this._cachedCompiledPatterns.setSource(0,n)),this._cachedCompiledPatterns}},ti=class extends Ur{constructor(t,n,r,o,i,l,s,a,u){super(t,n,r,o);x(this,"_begin");x(this,"beginCaptures");x(this,"whileCaptures");x(this,"_while");x(this,"whileHasBackReferences");x(this,"hasMissingPatterns");x(this,"patterns");x(this,"_cachedCompiledPatterns");x(this,"_cachedCompiledWhilePatterns");this._begin=new br(i,this.id),this.beginCaptures=l,this.whileCaptures=a,this._while=new br(s,Dp),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=u.patterns,this.hasMissingPatterns=u.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(t,n){return this._while.resolveBackReferences(t,n)}collectPatterns(t,n){n.push(this._begin)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new Or;for(const n of this.patterns)t.getRule(n).collectPatterns(t,this._cachedCompiledPatterns)}return this._cachedCompiledPatterns}compileWhile(t,n){return this._getCachedCompiledWhilePatterns(t,n).compile(t)}compileWhileAG(t,n,r,o){return this._getCachedCompiledWhilePatterns(t,n).compileAG(t,r,o)}_getCachedCompiledWhilePatterns(t,n){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new Or,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,n||"￿"),this._cachedCompiledWhilePatterns}},jp=class me{static createCaptureRule(t,n,r,o,i){return t.registerRule(l=>new C_(n,l,r,o,i))}static getCompiledRuleId(t,n,r){return t.id||n.registerRule(o=>{if(t.id=o,t.match)return new P_(t.$vscodeTextmateLocation,t.id,t.name,t.match,me._compileCaptures(t.captures,n,r));if(typeof t.begin>"u"){t.repository&&(r=Cp({},r,t.repository));let i=t.patterns;return typeof i>"u"&&t.include&&(i=[{include:t.include}]),new ju(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,me._compilePatterns(i,n,r))}return t.while?new ti(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,me._compileCaptures(t.beginCaptures||t.captures,n,r),t.while,me._compileCaptures(t.whileCaptures||t.captures,n,r),me._compilePatterns(t.patterns,n,r)):new ss(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,me._compileCaptures(t.beginCaptures||t.captures,n,r),t.end,me._compileCaptures(t.endCaptures||t.captures,n,r),t.applyEndPatternLast,me._compilePatterns(t.patterns,n,r))}),t.id}static _compileCaptures(t,n,r){let o=[];if(t){let i=0;for(const l in t){if(l==="$vscodeTextmateLocation")continue;const s=parseInt(l,10);s>i&&(i=s)}for(let l=0;l<=i;l++)o[l]=null;for(const l in t){if(l==="$vscodeTextmateLocation")continue;const s=parseInt(l,10);let a=0;t[l].patterns&&(a=me.getCompiledRuleId(t[l],n,r)),o[s]=me.createCaptureRule(n,t[l].$vscodeTextmateLocation,t[l].name,t[l].contentName,a)}}return o}static _compilePatterns(t,n,r){let o=[];if(t)for(let i=0,l=t.length;i<l;i++){const s=t[i];let a=-1;if(s.include){const u=Op(s.include);switch(u.kind){case 0:case 1:a=me.getCompiledRuleId(r[s.include],n,r);break;case 2:let d=r[u.ruleName];d&&(a=me.getCompiledRuleId(d,n,r));break;case 3:case 4:const f=u.scopeName,m=u.kind===4?u.ruleName:null,g=n.getExternalGrammar(f,r);if(g)if(m){let v=g.repository[m];v&&(a=me.getCompiledRuleId(v,n,g.repository))}else a=me.getCompiledRuleId(g.repository.$self,n,g.repository);break}}else a=me.getCompiledRuleId(s,n,r);if(a!==-1){const u=n.getRule(a);let d=!1;if((u instanceof ju||u instanceof ss||u instanceof ti)&&u.hasMissingPatterns&&u.patterns.length===0&&(d=!0),d)continue;o.push(a)}}return{patterns:o,hasMissingPatterns:(t?t.length:0)!==o.length}}},br=class Mp{constructor(t,n){x(this,"source");x(this,"ruleId");x(this,"hasAnchor");x(this,"hasBackReferences");x(this,"_anchorCache");if(t&&typeof t=="string"){const r=t.length;let o=0,i=[],l=!1;for(let s=0;s<r;s++)if(t.charAt(s)==="\\"&&s+1<r){const u=t.charAt(s+1);u==="z"?(i.push(t.substring(o,s)),i.push("$(?!\\n)(?<!\\n)"),o=s+2):(u==="A"||u==="G")&&(l=!0),s++}this.hasAnchor=l,o===0?this.source=t:(i.push(t.substring(o,r)),this.source=i.join(""))}else this.hasAnchor=!1,this.source=t;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=n,typeof this.source=="string"?this.hasBackReferences=x_.test(this.source):this.hasBackReferences=!1}clone(){return new Mp(this.source,this.ruleId)}setSource(t){this.source!==t&&(this.source=t,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(t,n){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let r=n.map(o=>t.substring(o.start,o.end));return Du.lastIndex=0,this.source.replace(Du,(o,i)=>Tp(r[parseInt(i,10)]||""))}_buildAnchorCache(){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let t=[],n=[],r=[],o=[],i,l,s,a;for(i=0,l=this.source.length;i<l;i++)s=this.source.charAt(i),t[i]=s,n[i]=s,r[i]=s,o[i]=s,s==="\\"&&i+1<l&&(a=this.source.charAt(i+1),a==="A"?(t[i+1]="￿",n[i+1]="￿",r[i+1]="A",o[i+1]="A"):a==="G"?(t[i+1]="￿",n[i+1]="G",r[i+1]="￿",o[i+1]="G"):(t[i+1]=a,n[i+1]=a,r[i+1]=a,o[i+1]=a),i++);return{A0_G0:t.join(""),A0_G1:n.join(""),A1_G0:r.join(""),A1_G1:o.join("")}}resolveAnchors(t,n){return!this.hasAnchor||!this._anchorCache||typeof this.source!="string"?this.source:t?n?this._anchorCache.A1_G1:this._anchorCache.A1_G0:n?this._anchorCache.A0_G1:this._anchorCache.A0_G0}},Or=class{constructor(){x(this,"_items");x(this,"_hasAnchors");x(this,"_cached");x(this,"_anchorCache");this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(e){this._items.push(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}unshift(e){this._items.unshift(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}length(){return this._items.length}setSource(e,t){this._items[e].source!==t&&(this._disposeCaches(),this._items[e].setSource(t))}compile(e){if(!this._cached){let t=this._items.map(n=>n.source);this._cached=new Mu(e,t,this._items.map(n=>n.ruleId))}return this._cached}compileAG(e,t,n){return this._hasAnchors?t?n?(this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G1):(this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G0):n?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G0):this.compile(e)}_resolveAnchors(e,t,n){let r=this._items.map(o=>o.resolveAnchors(t,n));return new Mu(e,r,this._items.map(o=>o.ruleId))}},Mu=class{constructor(e,t,n){x(this,"scanner");this.regExps=t,this.rules=n,this.scanner=e.createOnigScanner(t)}dispose(){typeof this.scanner.dispose=="function"&&this.scanner.dispose()}toString(){const e=[];for(let t=0,n=this.rules.length;t<n;t++)e.push("   - "+this.rules[t]+": "+this.regExps[t]);return e.join(`
`)}findNextMatchSync(e,t,n){const r=this.scanner.findNextMatchSync(e,t,n);return r?{ruleId:this.rules[r.index],captureIndices:r.captureIndices}:null}},ll=class{constructor(e,t){this.languageId=e,this.tokenType=t}},at,R_=(at=class{constructor(t,n){x(this,"_defaultAttributes");x(this,"_embeddedLanguagesMatcher");x(this,"_getBasicScopeAttributes",new Ip(t=>{const n=this._scopeToLanguage(t),r=this._toStandardTokenType(t);return new ll(n,r)}));this._defaultAttributes=new ll(t,8),this._embeddedLanguagesMatcher=new L_(Object.entries(n||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(t){return t===null?at._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(t)}_scopeToLanguage(t){return this._embeddedLanguagesMatcher.match(t)||0}_toStandardTokenType(t){const n=t.match(at.STANDARD_TOKEN_TYPE_REGEXP);if(!n)return 8;switch(n[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw new Error("Unexpected match for standard token type!")}},x(at,"_NULL_SCOPE_METADATA",new ll(0,0)),x(at,"STANDARD_TOKEN_TYPE_REGEXP",/\b(comment|string|regex|meta\.embedded)\b/),at),L_=class{constructor(e){x(this,"values");x(this,"scopesRegExp");if(e.length===0)this.values=null,this.scopesRegExp=null;else{this.values=new Map(e);const t=e.map(([n,r])=>Tp(n));t.sort(),t.reverse(),this.scopesRegExp=new RegExp(`^((${t.join(")|(")}))($|\\.)`,"")}}match(e){if(!this.scopesRegExp)return;const t=e.match(this.scopesRegExp);if(t)return this.values.get(t[1])}},Vu=class{constructor(e,t){this.stack=e,this.stoppedEarly=t}};function Vp(e,t,n,r,o,i,l,s){const a=t.content.length;let u=!1,d=-1;if(l){const g=T_(e,t,n,r,o,i);o=g.stack,r=g.linePos,n=g.isFirstLine,d=g.anchorPosition}const f=Date.now();for(;!u;){if(s!==0&&Date.now()-f>s)return new Vu(o,!0);m()}return new Vu(o,!1);function m(){const g=I_(e,t,n,r,o,d);if(!g){i.produce(o,a),u=!0;return}const v=g.captureIndices,E=g.matchedRuleId,w=v&&v.length>0?v[0].end>r:!1;if(E===k_){const h=o.getRule(e);i.produce(o,v[0].start),o=o.withContentNameScopesList(o.nameScopesList),rr(e,t,n,o,i,h.endCaptures,v),i.produce(o,v[0].end);const c=o;if(o=o.parent,d=c.getAnchorPos(),!w&&c.getEnterPos()===r){o=c,i.produce(o,a),u=!0;return}}else{const h=e.getRule(E);i.produce(o,v[0].start);const c=o,_=h.getName(t.content,v),S=o.contentNameScopesList.pushAttributed(_,e);if(o=o.push(E,r,d,v[0].end===a,null,S,S),h instanceof ss){const C=h;rr(e,t,n,o,i,C.beginCaptures,v),i.produce(o,v[0].end),d=v[0].end;const L=C.getContentName(t.content,v),R=S.pushAttributed(L,e);if(o=o.withContentNameScopesList(R),C.endHasBackReferences&&(o=o.withEndRule(C.getEndWithResolvedBackReferences(t.content,v))),!w&&c.hasSameRuleAs(o)){o=o.pop(),i.produce(o,a),u=!0;return}}else if(h instanceof ti){const C=h;rr(e,t,n,o,i,C.beginCaptures,v),i.produce(o,v[0].end),d=v[0].end;const L=C.getContentName(t.content,v),R=S.pushAttributed(L,e);if(o=o.withContentNameScopesList(R),C.whileHasBackReferences&&(o=o.withEndRule(C.getWhileWithResolvedBackReferences(t.content,v))),!w&&c.hasSameRuleAs(o)){o=o.pop(),i.produce(o,a),u=!0;return}}else if(rr(e,t,n,o,i,h.captures,v),i.produce(o,v[0].end),o=o.pop(),!w){o=o.safePop(),i.produce(o,a),u=!0;return}}v[0].end>r&&(r=v[0].end,n=!1)}}function T_(e,t,n,r,o,i){let l=o.beginRuleCapturedEOL?0:-1;const s=[];for(let a=o;a;a=a.pop()){const u=a.getRule(e);u instanceof ti&&s.push({rule:u,stack:a})}for(let a=s.pop();a;a=s.pop()){const{ruleScanner:u,findOptions:d}=b_(a.rule,e,a.stack.endRule,n,r===l),f=u.findNextMatchSync(t,r,d);if(f){if(f.ruleId!==Dp){o=a.stack.pop();break}f.captureIndices&&f.captureIndices.length&&(i.produce(a.stack,f.captureIndices[0].start),rr(e,t,n,a.stack,i,a.rule.whileCaptures,f.captureIndices),i.produce(a.stack,f.captureIndices[0].end),l=f.captureIndices[0].end,f.captureIndices[0].end>r&&(r=f.captureIndices[0].end,n=!1))}else{o=a.stack.pop();break}}return{stack:o,linePos:r,anchorPosition:l,isFirstLine:n}}function I_(e,t,n,r,o,i){const l=A_(e,t,n,r,o,i),s=e.getInjections();if(s.length===0)return l;const a=N_(s,e,t,n,r,o,i);if(!a)return l;if(!l)return a;const u=l.captureIndices[0].start,d=a.captureIndices[0].start;return d<u||a.priorityMatch&&d===u?a:l}function A_(e,t,n,r,o,i){const l=o.getRule(e),{ruleScanner:s,findOptions:a}=zp(l,e,o.endRule,n,r===i),u=s.findNextMatchSync(t,r,a);return u?{captureIndices:u.captureIndices,matchedRuleId:u.ruleId}:null}function N_(e,t,n,r,o,i,l){let s=Number.MAX_VALUE,a=null,u,d=0;const f=i.contentNameScopesList.getScopeNames();for(let m=0,g=e.length;m<g;m++){const v=e[m];if(!v.matcher(f))continue;const E=t.getRule(v.ruleId),{ruleScanner:w,findOptions:h}=zp(E,t,null,r,o===l),c=w.findNextMatchSync(n,o,h);if(!c)continue;const _=c.captureIndices[0].start;if(!(_>=s)&&(s=_,a=c.captureIndices,u=c.ruleId,d=v.priority,s===o))break}return a?{priorityMatch:d===-1,captureIndices:a,matchedRuleId:u}:null}function zp(e,t,n,r,o){return{ruleScanner:e.compileAG(t,n,r,o),findOptions:0}}function b_(e,t,n,r,o){return{ruleScanner:e.compileWhileAG(t,n,r,o),findOptions:0}}function rr(e,t,n,r,o,i,l){if(i.length===0)return;const s=t.content,a=Math.min(i.length,l.length),u=[],d=l[0].end;for(let f=0;f<a;f++){const m=i[f];if(m===null)continue;const g=l[f];if(g.length===0)continue;if(g.start>d)break;for(;u.length>0&&u[u.length-1].endPos<=g.start;)o.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop();if(u.length>0?o.produceFromScopes(u[u.length-1].scopes,g.start):o.produce(r,g.start),m.retokenizeCapturedWithRuleId){const E=m.getName(s,l),w=r.contentNameScopesList.pushAttributed(E,e),h=m.getContentName(s,l),c=w.pushAttributed(h,e),_=r.push(m.retokenizeCapturedWithRuleId,g.start,-1,!1,null,w,c),S=e.createOnigString(s.substring(0,g.end));Vp(e,S,n&&g.start===0,g.start,_,o,!1,0),bp(S);continue}const v=m.getName(s,l);if(v!==null){const w=(u.length>0?u[u.length-1].scopes:r.contentNameScopesList).pushAttributed(v,e);u.push(new O_(w,g.end))}}for(;u.length>0;)o.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop()}var O_=class{constructor(e,t){x(this,"scopes");x(this,"endPos");this.scopes=e,this.endPos=t}};function D_(e,t,n,r,o,i,l,s){return new M_(e,t,n,r,o,i,l,s)}function zu(e,t,n,r,o){const i=Zo(t,ni),l=jp.getCompiledRuleId(n,r,o.repository);for(const s of i)e.push({debugSelector:t,matcher:s.matcher,ruleId:l,grammar:o,priority:s.priority})}function ni(e,t){if(t.length<e.length)return!1;let n=0;return e.every(r=>{for(let o=n;o<t.length;o++)if(j_(t[o],r))return n=o+1,!0;return!1})}function j_(e,t){if(!e)return!1;if(e===t)return!0;const n=t.length;return e.length>n&&e.substr(0,n)===t&&e[n]==="."}var M_=class{constructor(e,t,n,r,o,i,l,s){x(this,"_rootId");x(this,"_lastRuleId");x(this,"_ruleId2desc");x(this,"_includedGrammars");x(this,"_grammarRepository");x(this,"_grammar");x(this,"_injections");x(this,"_basicScopeAttributesProvider");x(this,"_tokenTypeMatchers");if(this._rootScopeName=e,this.balancedBracketSelectors=i,this._onigLib=s,this._basicScopeAttributesProvider=new R_(n,r),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=l,this._grammar=Bu(t,null),this._injections=null,this._tokenTypeMatchers=[],o)for(const a of Object.keys(o)){const u=Zo(a,ni);for(const d of u)this._tokenTypeMatchers.push({matcher:d.matcher,type:o[a]})}}get themeProvider(){return this._grammarRepository}dispose(){for(const e of this._ruleId2desc)e&&e.dispose()}createOnigScanner(e){return this._onigLib.createOnigScanner(e)}createOnigString(e){return this._onigLib.createOnigString(e)}getMetadataForScope(e){return this._basicScopeAttributesProvider.getBasicScopeAttributes(e)}_collectInjections(){const e={lookup:o=>o===this._rootScopeName?this._grammar:this.getExternalGrammar(o),injections:o=>this._grammarRepository.injections(o)},t=[],n=this._rootScopeName,r=e.lookup(n);if(r){const o=r.injections;if(o)for(let l in o)zu(t,l,o[l],this,r);const i=this._grammarRepository.injections(n);i&&i.forEach(l=>{const s=this.getExternalGrammar(l);if(s){const a=s.injectionSelector;a&&zu(t,a,s,this,s)}})}return t.sort((o,i)=>o.priority-i.priority),t}getInjections(){return this._injections===null&&(this._injections=this._collectInjections()),this._injections}registerRule(e){const t=++this._lastRuleId,n=e(t);return this._ruleId2desc[t]=n,n}getRule(e){return this._ruleId2desc[e]}getExternalGrammar(e,t){if(this._includedGrammars[e])return this._includedGrammars[e];if(this._grammarRepository){const n=this._grammarRepository.lookup(e);if(n)return this._includedGrammars[e]=Bu(n,t&&t.$base),this._includedGrammars[e]}}tokenizeLine(e,t,n=0){const r=this._tokenize(e,t,!1,n);return{tokens:r.lineTokens.getResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}tokenizeLine2(e,t,n=0){const r=this._tokenize(e,t,!0,n);return{tokens:r.lineTokens.getBinaryResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}_tokenize(e,t,n,r){this._rootId===-1&&(this._rootId=jp.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections());let o;if(!t||t===as.NULL){o=!0;const u=this._basicScopeAttributesProvider.getDefaultAttributes(),d=this.themeProvider.getDefaults(),f=Vn.set(0,u.languageId,u.tokenType,null,d.fontStyle,d.foregroundId,d.backgroundId),m=this.getRule(this._rootId).getName(null,null);let g;m?g=fr.createRootAndLookUpScopeName(m,f,this):g=fr.createRoot("unknown",f),t=new as(null,this._rootId,-1,-1,!1,null,g,g)}else o=!1,t.reset();e=e+`
`;const i=this.createOnigString(e),l=i.content.length,s=new z_(n,e,this._tokenTypeMatchers,this.balancedBracketSelectors),a=Vp(this,i,o,0,t,s,!0,r);return bp(i),{lineLength:l,lineTokens:s,ruleStack:a.stack,stoppedEarly:a.stoppedEarly}}};function Bu(e,t){return e=n_(e),e.repository=e.repository||{},e.repository.$self={$vscodeTextmateLocation:e.$vscodeTextmateLocation,patterns:e.patterns,name:e.scopeName},e.repository.$base=t||e.repository.$self,e}var fr=class et{constructor(t,n,r){this.parent=t,this.scopePath=n,this.tokenAttributes=r}static fromExtension(t,n){let r=t,o=(t==null?void 0:t.scopePath)??null;for(const i of n)o=il.push(o,i.scopeNames),r=new et(r,o,i.encodedTokenAttributes);return r}static createRoot(t,n){return new et(null,new il(null,t),n)}static createRootAndLookUpScopeName(t,n,r){const o=r.getMetadataForScope(t),i=new il(null,t),l=r.themeProvider.themeMatch(i),s=et.mergeAttributes(n,o,l);return new et(null,i,s)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(t){return et.equals(this,t)}static equals(t,n){do{if(t===n||!t&&!n)return!0;if(!t||!n||t.scopeName!==n.scopeName||t.tokenAttributes!==n.tokenAttributes)return!1;t=t.parent,n=n.parent}while(!0)}static mergeAttributes(t,n,r){let o=-1,i=0,l=0;return r!==null&&(o=r.fontStyle,i=r.foregroundId,l=r.backgroundId),Vn.set(t,n.languageId,n.tokenType,null,o,i,l)}pushAttributed(t,n){if(t===null)return this;if(t.indexOf(" ")===-1)return et._pushAttributed(this,t,n);const r=t.split(/ /g);let o=this;for(const i of r)o=et._pushAttributed(o,i,n);return o}static _pushAttributed(t,n,r){const o=r.getMetadataForScope(n),i=t.scopePath.push(n),l=r.themeProvider.themeMatch(i),s=et.mergeAttributes(t.tokenAttributes,o,l);return new et(t,i,s)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(t){var o;const n=[];let r=this;for(;r&&r!==t;)n.push({encodedTokenAttributes:r.tokenAttributes,scopeNames:r.scopePath.getExtensionIfDefined(((o=r.parent)==null?void 0:o.scopePath)??null)}),r=r.parent;return r===t?n.reverse():void 0}},Be,as=(Be=class{constructor(t,n,r,o,i,l,s,a){x(this,"_stackElementBrand");x(this,"_enterPos");x(this,"_anchorPos");x(this,"depth");this.parent=t,this.ruleId=n,this.beginRuleCapturedEOL=i,this.endRule=l,this.nameScopesList=s,this.contentNameScopesList=a,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=r,this._anchorPos=o}equals(t){return t===null?!1:Be._equals(this,t)}static _equals(t,n){return t===n?!0:this._structuralEquals(t,n)?fr.equals(t.contentNameScopesList,n.contentNameScopesList):!1}static _structuralEquals(t,n){do{if(t===n||!t&&!n)return!0;if(!t||!n||t.depth!==n.depth||t.ruleId!==n.ruleId||t.endRule!==n.endRule)return!1;t=t.parent,n=n.parent}while(!0)}clone(){return this}static _reset(t){for(;t;)t._enterPos=-1,t._anchorPos=-1,t=t.parent}reset(){Be._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(t,n,r,o,i,l,s){return new Be(this,t,n,r,o,i,l,s)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(t){return t.getRule(this.ruleId)}toString(){const t=[];return this._writeString(t,0),"["+t.join(",")+"]"}_writeString(t,n){var r,o;return this.parent&&(n=this.parent._writeString(t,n)),t[n++]=`(${this.ruleId}, ${(r=this.nameScopesList)==null?void 0:r.toString()}, ${(o=this.contentNameScopesList)==null?void 0:o.toString()})`,n}withContentNameScopesList(t){return this.contentNameScopesList===t?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,t)}withEndRule(t){return this.endRule===t?this:new Be(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,t,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(t){let n=this;for(;n&&n._enterPos===t._enterPos;){if(n.ruleId===t.ruleId)return!0;n=n.parent}return!1}toStateStackFrame(){var t,n,r;return{ruleId:this.ruleId,beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:((n=this.nameScopesList)==null?void 0:n.getExtensionIfDefined(((t=this.parent)==null?void 0:t.nameScopesList)??null))??[],contentNameScopesList:((r=this.contentNameScopesList)==null?void 0:r.getExtensionIfDefined(this.nameScopesList))??[]}}static pushFrame(t,n){const r=fr.fromExtension((t==null?void 0:t.nameScopesList)??null,n.nameScopesList);return new Be(t,n.ruleId,n.enterPos??-1,n.anchorPos??-1,n.beginRuleCapturedEOL,n.endRule,r,fr.fromExtension(r,n.contentNameScopesList))}},x(Be,"NULL",new Be(null,0,0,0,!1,null,null,null)),Be),V_=class{constructor(e,t){x(this,"balancedBracketScopes");x(this,"unbalancedBracketScopes");x(this,"allowAny",!1);this.balancedBracketScopes=e.flatMap(n=>n==="*"?(this.allowAny=!0,[]):Zo(n,ni).map(r=>r.matcher)),this.unbalancedBracketScopes=t.flatMap(n=>Zo(n,ni).map(r=>r.matcher))}get matchesAlways(){return this.allowAny&&this.unbalancedBracketScopes.length===0}get matchesNever(){return this.balancedBracketScopes.length===0&&!this.allowAny}match(e){for(const t of this.unbalancedBracketScopes)if(t(e))return!1;for(const t of this.balancedBracketScopes)if(t(e))return!0;return this.allowAny}},z_=class{constructor(e,t,n,r){x(this,"_emitBinaryTokens");x(this,"_lineText");x(this,"_tokens");x(this,"_binaryTokens");x(this,"_lastTokenEndIndex");x(this,"_tokenTypeOverrides");this.balancedBracketSelectors=r,this._emitBinaryTokens=e,this._tokenTypeOverrides=n,this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}produce(e,t){this.produceFromScopes(e.contentNameScopesList,t)}produceFromScopes(e,t){var r;if(this._lastTokenEndIndex>=t)return;if(this._emitBinaryTokens){let o=(e==null?void 0:e.tokenAttributes)??0,i=!1;if((r=this.balancedBracketSelectors)!=null&&r.matchesAlways&&(i=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){const l=(e==null?void 0:e.getScopeNames())??[];for(const s of this._tokenTypeOverrides)s.matcher(l)&&(o=Vn.set(o,0,s.type,null,-1,0,0));this.balancedBracketSelectors&&(i=this.balancedBracketSelectors.match(l))}if(i&&(o=Vn.set(o,0,8,i,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===o){this._lastTokenEndIndex=t;return}this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(o),this._lastTokenEndIndex=t;return}const n=(e==null?void 0:e.getScopeNames())??[];this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:t,scopes:n}),this._lastTokenEndIndex=t}getResult(e,t){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===t-1&&this._tokens.pop(),this._tokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(e,t){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===t-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),this._binaryTokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._binaryTokens[this._binaryTokens.length-2]=0);const n=new Uint32Array(this._binaryTokens.length);for(let r=0,o=this._binaryTokens.length;r<o;r++)n[r]=this._binaryTokens[r];return n}},B_=class{constructor(e,t){x(this,"_grammars",new Map);x(this,"_rawGrammars",new Map);x(this,"_injectionGrammars",new Map);x(this,"_theme");this._onigLib=t,this._theme=e}dispose(){for(const e of this._grammars.values())e.dispose()}setTheme(e){this._theme=e}getColorMap(){return this._theme.getColorMap()}addGrammar(e,t){this._rawGrammars.set(e.scopeName,e),t&&this._injectionGrammars.set(e.scopeName,t)}lookup(e){return this._rawGrammars.get(e)}injections(e){return this._injectionGrammars.get(e)}getDefaults(){return this._theme.getDefaults()}themeMatch(e){return this._theme.match(e)}grammarForScopeName(e,t,n,r,o){if(!this._grammars.has(e)){let i=this._rawGrammars.get(e);if(!i)return null;this._grammars.set(e,D_(e,i,t,n,r,o,this,this._onigLib))}return this._grammars.get(e)}},$_=class{constructor(t){x(this,"_options");x(this,"_syncRegistry");x(this,"_ensureGrammarCache");this._options=t,this._syncRegistry=new B_(Jo.createFromRawTheme(t.theme,t.colorMap),t.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(t,n){this._syncRegistry.setTheme(Jo.createFromRawTheme(t,n))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(t,n,r){return this.loadGrammarWithConfiguration(t,n,{embeddedLanguages:r})}loadGrammarWithConfiguration(t,n,r){return this._loadGrammar(t,n,r.embeddedLanguages,r.tokenTypes,new V_(r.balancedBracketSelectors||[],r.unbalancedBracketSelectors||[]))}loadGrammar(t){return this._loadGrammar(t,0,null,null,null)}_loadGrammar(t,n,r,o,i){const l=new g_(this._syncRegistry,t);for(;l.Q.length>0;)l.Q.map(s=>this._loadSingleGrammar(s.scopeName)),l.processQueue();return this._grammarForScopeName(t,n,r,o,i)}_loadSingleGrammar(t){this._ensureGrammarCache.has(t)||(this._doLoadSingleGrammar(t),this._ensureGrammarCache.set(t,!0))}_doLoadSingleGrammar(t){const n=this._options.loadGrammar(t);if(n){const r=typeof this._options.getInjections=="function"?this._options.getInjections(t):void 0;this._syncRegistry.addGrammar(n,r)}}addGrammar(t,n=[],r=0,o=null){return this._syncRegistry.addGrammar(t,n),this._grammarForScopeName(t.scopeName,r,o)}_grammarForScopeName(t,n=0,r=null,o=null,i=null){return this._syncRegistry.grammarForScopeName(t,n,r,o,i)}},us=as.NULL;const U_=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];class Fr{constructor(t,n,r){this.normal=n,this.property=t,r&&(this.space=r)}}Fr.prototype.normal={};Fr.prototype.property={};Fr.prototype.space=void 0;function Bp(e,t){const n={},r={};for(const o of e)Object.assign(n,o.property),Object.assign(r,o.normal);return new Fr(n,r,t)}function cs(e){return e.toLowerCase()}class Le{constructor(t,n){this.attribute=n,this.property=t}}Le.prototype.attribute="";Le.prototype.booleanish=!1;Le.prototype.boolean=!1;Le.prototype.commaOrSpaceSeparated=!1;Le.prototype.commaSeparated=!1;Le.prototype.defined=!1;Le.prototype.mustUseProperty=!1;Le.prototype.number=!1;Le.prototype.overloadedBoolean=!1;Le.prototype.property="";Le.prototype.spaceSeparated=!1;Le.prototype.space=void 0;let F_=0;const D=an(),J=an(),ds=an(),P=an(),$=an(),Ln=an(),Te=an();function an(){return 2**++F_}const ps=Object.freeze(Object.defineProperty({__proto__:null,boolean:D,booleanish:J,commaOrSpaceSeparated:Te,commaSeparated:Ln,number:P,overloadedBoolean:ds,spaceSeparated:$},Symbol.toStringTag,{value:"Module"})),sl=Object.keys(ps);class fa extends Le{constructor(t,n,r,o){let i=-1;if(super(t,n),$u(this,"space",o),typeof r=="number")for(;++i<sl.length;){const l=sl[i];$u(this,sl[i],(r&ps[l])===ps[l])}}}fa.prototype.defined=!0;function $u(e,t,n){n&&(e[t]=n)}function Un(e){const t={},n={};for(const[r,o]of Object.entries(e.properties)){const i=new fa(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[cs(r)]=r,n[cs(i.attribute)]=r}return new Fr(t,n,e.space)}const $p=Un({properties:{ariaActiveDescendant:null,ariaAtomic:J,ariaAutoComplete:null,ariaBusy:J,ariaChecked:J,ariaColCount:P,ariaColIndex:P,ariaColSpan:P,ariaControls:$,ariaCurrent:null,ariaDescribedBy:$,ariaDetails:null,ariaDisabled:J,ariaDropEffect:$,ariaErrorMessage:null,ariaExpanded:J,ariaFlowTo:$,ariaGrabbed:J,ariaHasPopup:null,ariaHidden:J,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:$,ariaLevel:P,ariaLive:null,ariaModal:J,ariaMultiLine:J,ariaMultiSelectable:J,ariaOrientation:null,ariaOwns:$,ariaPlaceholder:null,ariaPosInSet:P,ariaPressed:J,ariaReadOnly:J,ariaRelevant:null,ariaRequired:J,ariaRoleDescription:$,ariaRowCount:P,ariaRowIndex:P,ariaRowSpan:P,ariaSelected:J,ariaSetSize:P,ariaSort:null,ariaValueMax:P,ariaValueMin:P,ariaValueNow:P,ariaValueText:null,role:null},transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function Up(e,t){return t in e?e[t]:t}function Fp(e,t){return Up(e,t.toLowerCase())}const G_=Un({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Ln,acceptCharset:$,accessKey:$,action:null,allow:null,allowFullScreen:D,allowPaymentRequest:D,allowUserMedia:D,alt:null,as:null,async:D,autoCapitalize:null,autoComplete:$,autoFocus:D,autoPlay:D,blocking:$,capture:null,charSet:null,checked:D,cite:null,className:$,cols:P,colSpan:null,content:null,contentEditable:J,controls:D,controlsList:$,coords:P|Ln,crossOrigin:null,data:null,dateTime:null,decoding:null,default:D,defer:D,dir:null,dirName:null,disabled:D,download:ds,draggable:J,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:D,formTarget:null,headers:$,height:P,hidden:ds,high:P,href:null,hrefLang:null,htmlFor:$,httpEquiv:$,id:null,imageSizes:null,imageSrcSet:null,inert:D,inputMode:null,integrity:null,is:null,isMap:D,itemId:null,itemProp:$,itemRef:$,itemScope:D,itemType:$,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:D,low:P,manifest:null,max:null,maxLength:P,media:null,method:null,min:null,minLength:P,multiple:D,muted:D,name:null,nonce:null,noModule:D,noValidate:D,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:D,optimum:P,pattern:null,ping:$,placeholder:null,playsInline:D,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:D,referrerPolicy:null,rel:$,required:D,reversed:D,rows:P,rowSpan:P,sandbox:$,scope:null,scoped:D,seamless:D,selected:D,shadowRootClonable:D,shadowRootDelegatesFocus:D,shadowRootMode:null,shape:null,size:P,sizes:null,slot:null,span:P,spellCheck:J,src:null,srcDoc:null,srcLang:null,srcSet:null,start:P,step:null,style:null,tabIndex:P,target:null,title:null,translate:null,type:null,typeMustMatch:D,useMap:null,value:J,width:P,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:$,axis:null,background:null,bgColor:null,border:P,borderColor:null,bottomMargin:P,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:D,declare:D,event:null,face:null,frame:null,frameBorder:null,hSpace:P,leftMargin:P,link:null,longDesc:null,lowSrc:null,marginHeight:P,marginWidth:P,noResize:D,noHref:D,noShade:D,noWrap:D,object:null,profile:null,prompt:null,rev:null,rightMargin:P,rules:null,scheme:null,scrolling:J,standby:null,summary:null,text:null,topMargin:P,valueType:null,version:null,vAlign:null,vLink:null,vSpace:P,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:D,disableRemotePlayback:D,prefix:null,property:null,results:P,security:null,unselectable:null},space:"html",transform:Fp}),H_=Un({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:Te,accentHeight:P,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:P,amplitude:P,arabicForm:null,ascent:P,attributeName:null,attributeType:null,azimuth:P,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:P,by:null,calcMode:null,capHeight:P,className:$,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:P,diffuseConstant:P,direction:null,display:null,dur:null,divisor:P,dominantBaseline:null,download:D,dx:null,dy:null,edgeMode:null,editable:null,elevation:P,enableBackground:null,end:null,event:null,exponent:P,externalResourcesRequired:null,fill:null,fillOpacity:P,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Ln,g2:Ln,glyphName:Ln,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:P,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:P,horizOriginX:P,horizOriginY:P,id:null,ideographic:P,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:P,k:P,k1:P,k2:P,k3:P,k4:P,kernelMatrix:Te,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:P,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:P,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:P,overlineThickness:P,paintOrder:null,panose1:null,path:null,pathLength:P,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:$,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:P,pointsAtY:P,pointsAtZ:P,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Te,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Te,rev:Te,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Te,requiredFeatures:Te,requiredFonts:Te,requiredFormats:Te,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:P,specularExponent:P,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:P,strikethroughThickness:P,string:null,stroke:null,strokeDashArray:Te,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:P,strokeOpacity:P,strokeWidth:null,style:null,surfaceScale:P,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Te,tabIndex:P,tableValues:null,target:null,targetX:P,targetY:P,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Te,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:P,underlineThickness:P,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:P,values:null,vAlphabetic:P,vMathematical:P,vectorEffect:null,vHanging:P,vIdeographic:P,version:null,vertAdvY:P,vertOriginX:P,vertOriginY:P,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:P,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Up}),Gp=Un({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),Hp=Un({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Fp}),Wp=Un({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),W_=/[A-Z]/g,Uu=/-[a-z]/g,K_=/^data[-\w.:]+$/i;function Q_(e,t){const n=cs(t);let r=t,o=Le;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&K_.test(t)){if(t.charAt(4)==="-"){const i=t.slice(5).replace(Uu,Y_);r="data"+i.charAt(0).toUpperCase()+i.slice(1)}else{const i=t.slice(4);if(!Uu.test(i)){let l=i.replace(W_,q_);l.charAt(0)!=="-"&&(l="-"+l),t="data"+l}}o=fa}return new o(r,t)}function q_(e){return"-"+e.toLowerCase()}function Y_(e){return e.charAt(1).toUpperCase()}const X_=Bp([$p,G_,Gp,Hp,Wp],"html"),Kp=Bp([$p,H_,Gp,Hp,Wp],"svg"),Fu={}.hasOwnProperty;function J_(e,t){const n=t||{};function r(o,...i){let l=r.invalid;const s=r.handlers;if(o&&Fu.call(o,e)){const a=String(o[e]);l=Fu.call(s,a)?s[a]:r.unknown}if(l)return l.call(this,o,...i)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}const Z_=/["&'<>`]/g,ey=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,ty=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,ny=/[|\\{}()[\]^$+*?.]/g,Gu=new WeakMap;function ry(e,t){if(e=e.replace(t.subset?oy(t.subset):Z_,r),t.subset||t.escapeOnly)return e;return e.replace(ey,n).replace(ty,r);function n(o,i,l){return t.format((o.charCodeAt(0)-55296)*1024+o.charCodeAt(1)-56320+65536,l.charCodeAt(i+2),t)}function r(o,i,l){return t.format(o.charCodeAt(0),l.charCodeAt(i+1),t)}}function oy(e){let t=Gu.get(e);return t||(t=iy(e),Gu.set(e,t)),t}function iy(e){const t=[];let n=-1;for(;++n<e.length;)t.push(e[n].replace(ny,"\\$&"));return new RegExp("(?:"+t.join("|")+")","g")}const ly=/[\dA-Fa-f]/;function sy(e,t,n){const r="&#x"+e.toString(16).toUpperCase();return n&&t&&!ly.test(String.fromCharCode(t))?r:r+";"}const ay=/\d/;function uy(e,t,n){const r="&#"+String(e);return n&&t&&!ay.test(String.fromCharCode(t))?r:r+";"}const cy=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],al={nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},dy=["cent","copy","divide","gt","lt","not","para","times"],Qp={}.hasOwnProperty,fs={};let po;for(po in al)Qp.call(al,po)&&(fs[al[po]]=po);const py=/[^\dA-Za-z]/;function fy(e,t,n,r){const o=String.fromCharCode(e);if(Qp.call(fs,o)){const i=fs[o],l="&"+i;return n&&cy.includes(i)&&!dy.includes(i)&&(!r||t&&t!==61&&py.test(String.fromCharCode(t)))?l:l+";"}return""}function my(e,t,n){let r=sy(e,t,n.omitOptionalSemicolons),o;if((n.useNamedReferences||n.useShortestReferences)&&(o=fy(e,t,n.omitOptionalSemicolons,n.attribute)),(n.useShortestReferences||!o)&&n.useShortestReferences){const i=uy(e,t,n.omitOptionalSemicolons);i.length<r.length&&(r=i)}return o&&(!n.useShortestReferences||o.length<r.length)?o:r}function Tn(e,t){return ry(e,Object.assign({format:my},t))}const hy=/^>|^->|<!--|-->|--!>|<!-$/g,gy=[">"],_y=["<",">"];function yy(e,t,n,r){return r.settings.bogusComments?"<?"+Tn(e.value,Object.assign({},r.settings.characterReferences,{subset:gy}))+">":"<!--"+e.value.replace(hy,o)+"-->";function o(i){return Tn(i,Object.assign({},r.settings.characterReferences,{subset:_y}))}}function vy(e,t,n,r){return"<!"+(r.settings.upperDoctype?"DOCTYPE":"doctype")+(r.settings.tightDoctype?"":" ")+"html>"}function Hu(e,t){const n=String(e);if(typeof t!="string")throw new TypeError("Expected character");let r=0,o=n.indexOf(t);for(;o!==-1;)r++,o=n.indexOf(t,o+t.length);return r}function Ey(e,t){const n=t||{};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}function Sy(e){return e.join(" ").trim()}const wy=/[ \t\n\f\r]/g;function ma(e){return typeof e=="object"?e.type==="text"?Wu(e.value):!1:Wu(e)}function Wu(e){return e.replace(wy,"")===""}const re=Yp(1),qp=Yp(-1),xy=[];function Yp(e){return t;function t(n,r,o){const i=n?n.children:xy;let l=(r||0)+e,s=i[l];if(!o)for(;s&&ma(s);)l+=e,s=i[l];return s}}const ky={}.hasOwnProperty;function Xp(e){return t;function t(n,r,o){return ky.call(e,n.tagName)&&e[n.tagName](n,r,o)}}const ha=Xp({body:Py,caption:ul,colgroup:ul,dd:Iy,dt:Ty,head:ul,html:Cy,li:Ly,optgroup:Ay,option:Ny,p:Ry,rp:Ku,rt:Ku,tbody:Oy,td:Qu,tfoot:Dy,th:Qu,thead:by,tr:jy});function ul(e,t,n){const r=re(n,t,!0);return!r||r.type!=="comment"&&!(r.type==="text"&&ma(r.value.charAt(0)))}function Cy(e,t,n){const r=re(n,t);return!r||r.type!=="comment"}function Py(e,t,n){const r=re(n,t);return!r||r.type!=="comment"}function Ry(e,t,n){const r=re(n,t);return r?r.type==="element"&&(r.tagName==="address"||r.tagName==="article"||r.tagName==="aside"||r.tagName==="blockquote"||r.tagName==="details"||r.tagName==="div"||r.tagName==="dl"||r.tagName==="fieldset"||r.tagName==="figcaption"||r.tagName==="figure"||r.tagName==="footer"||r.tagName==="form"||r.tagName==="h1"||r.tagName==="h2"||r.tagName==="h3"||r.tagName==="h4"||r.tagName==="h5"||r.tagName==="h6"||r.tagName==="header"||r.tagName==="hgroup"||r.tagName==="hr"||r.tagName==="main"||r.tagName==="menu"||r.tagName==="nav"||r.tagName==="ol"||r.tagName==="p"||r.tagName==="pre"||r.tagName==="section"||r.tagName==="table"||r.tagName==="ul"):!n||!(n.type==="element"&&(n.tagName==="a"||n.tagName==="audio"||n.tagName==="del"||n.tagName==="ins"||n.tagName==="map"||n.tagName==="noscript"||n.tagName==="video"))}function Ly(e,t,n){const r=re(n,t);return!r||r.type==="element"&&r.tagName==="li"}function Ty(e,t,n){const r=re(n,t);return!!(r&&r.type==="element"&&(r.tagName==="dt"||r.tagName==="dd"))}function Iy(e,t,n){const r=re(n,t);return!r||r.type==="element"&&(r.tagName==="dt"||r.tagName==="dd")}function Ku(e,t,n){const r=re(n,t);return!r||r.type==="element"&&(r.tagName==="rp"||r.tagName==="rt")}function Ay(e,t,n){const r=re(n,t);return!r||r.type==="element"&&r.tagName==="optgroup"}function Ny(e,t,n){const r=re(n,t);return!r||r.type==="element"&&(r.tagName==="option"||r.tagName==="optgroup")}function by(e,t,n){const r=re(n,t);return!!(r&&r.type==="element"&&(r.tagName==="tbody"||r.tagName==="tfoot"))}function Oy(e,t,n){const r=re(n,t);return!r||r.type==="element"&&(r.tagName==="tbody"||r.tagName==="tfoot")}function Dy(e,t,n){return!re(n,t)}function jy(e,t,n){const r=re(n,t);return!r||r.type==="element"&&r.tagName==="tr"}function Qu(e,t,n){const r=re(n,t);return!r||r.type==="element"&&(r.tagName==="td"||r.tagName==="th")}const My=Xp({body:By,colgroup:$y,head:zy,html:Vy,tbody:Uy});function Vy(e){const t=re(e,-1);return!t||t.type!=="comment"}function zy(e){const t=new Set;for(const r of e.children)if(r.type==="element"&&(r.tagName==="base"||r.tagName==="title")){if(t.has(r.tagName))return!1;t.add(r.tagName)}const n=e.children[0];return!n||n.type==="element"}function By(e){const t=re(e,-1,!0);return!t||t.type!=="comment"&&!(t.type==="text"&&ma(t.value.charAt(0)))&&!(t.type==="element"&&(t.tagName==="meta"||t.tagName==="link"||t.tagName==="script"||t.tagName==="style"||t.tagName==="template"))}function $y(e,t,n){const r=qp(n,t),o=re(e,-1,!0);return n&&r&&r.type==="element"&&r.tagName==="colgroup"&&ha(r,n.children.indexOf(r),n)?!1:!!(o&&o.type==="element"&&o.tagName==="col")}function Uy(e,t,n){const r=qp(n,t),o=re(e,-1);return n&&r&&r.type==="element"&&(r.tagName==="thead"||r.tagName==="tbody")&&ha(r,n.children.indexOf(r),n)?!1:!!(o&&o.type==="element"&&o.tagName==="tr")}const fo={name:[[`	
\f\r &/=>`.split(""),`	
\f\r "&'/=>\``.split("")],[`\0	
\f\r "&'/<=>`.split(""),`\0	
\f\r "&'/<=>\``.split("")]],unquoted:[[`	
\f\r &>`.split(""),`\0	
\f\r "&'<=>\``.split("")],[`\0	
\f\r "&'<=>\``.split(""),`\0	
\f\r "&'<=>\``.split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]};function Fy(e,t,n,r){const o=r.schema,i=o.space==="svg"?!1:r.settings.omitOptionalTags;let l=o.space==="svg"?r.settings.closeEmptyElements:r.settings.voids.includes(e.tagName.toLowerCase());const s=[];let a;o.space==="html"&&e.tagName==="svg"&&(r.schema=Kp);const u=Gy(r,e.properties),d=r.all(o.space==="html"&&e.tagName==="template"?e.content:e);return r.schema=o,d&&(l=!1),(u||!i||!My(e,t,n))&&(s.push("<",e.tagName,u?" "+u:""),l&&(o.space==="svg"||r.settings.closeSelfClosing)&&(a=u.charAt(u.length-1),(!r.settings.tightSelfClosing||a==="/"||a&&a!=='"'&&a!=="'")&&s.push(" "),s.push("/")),s.push(">")),s.push(d),!l&&(!i||!ha(e,t,n))&&s.push("</"+e.tagName+">"),s.join("")}function Gy(e,t){const n=[];let r=-1,o;if(t){for(o in t)if(t[o]!==null&&t[o]!==void 0){const i=Hy(e,o,t[o]);i&&n.push(i)}}for(;++r<n.length;){const i=e.settings.tightAttributes?n[r].charAt(n[r].length-1):void 0;r!==n.length-1&&i!=='"'&&i!=="'"&&(n[r]+=" ")}return n.join("")}function Hy(e,t,n){const r=Q_(e.schema,t),o=e.settings.allowParseErrors&&e.schema.space==="html"?0:1,i=e.settings.allowDangerousCharacters?0:1;let l=e.quote,s;if(r.overloadedBoolean&&(n===r.attribute||n==="")?n=!0:(r.boolean||r.overloadedBoolean)&&(typeof n!="string"||n===r.attribute||n==="")&&(n=!!n),n==null||n===!1||typeof n=="number"&&Number.isNaN(n))return"";const a=Tn(r.attribute,Object.assign({},e.settings.characterReferences,{subset:fo.name[o][i]}));return n===!0||(n=Array.isArray(n)?(r.commaSeparated?Ey:Sy)(n,{padLeft:!e.settings.tightCommaSeparatedLists}):String(n),e.settings.collapseEmptyAttributes&&!n)?a:(e.settings.preferUnquoted&&(s=Tn(n,Object.assign({},e.settings.characterReferences,{attribute:!0,subset:fo.unquoted[o][i]}))),s!==n&&(e.settings.quoteSmart&&Hu(n,l)>Hu(n,e.alternative)&&(l=e.alternative),s=l+Tn(n,Object.assign({},e.settings.characterReferences,{subset:(l==="'"?fo.single:fo.double)[o][i],attribute:!0}))+l),a+(s&&"="+s))}const Wy=["<","&"];function Jp(e,t,n,r){return n&&n.type==="element"&&(n.tagName==="script"||n.tagName==="style")?e.value:Tn(e.value,Object.assign({},r.settings.characterReferences,{subset:Wy}))}function Ky(e,t,n,r){return r.settings.allowDangerousHtml?e.value:Jp(e,t,n,r)}function Qy(e,t,n,r){return r.all(e)}const qy=J_("type",{invalid:Yy,unknown:Xy,handlers:{comment:yy,doctype:vy,element:Fy,raw:Ky,root:Qy,text:Jp}});function Yy(e){throw new Error("Expected node, not `"+e+"`")}function Xy(e){const t=e;throw new Error("Cannot compile unknown node `"+t.type+"`")}const Jy={},Zy={},ev=[];function tv(e,t){const n=Jy,r=n.quote||'"',o=r==='"'?"'":'"';if(r!=='"'&&r!=="'")throw new Error("Invalid quote `"+r+"`, expected `'` or `\"`");return{one:nv,all:rv,settings:{omitOptionalTags:n.omitOptionalTags||!1,allowParseErrors:n.allowParseErrors||!1,allowDangerousCharacters:n.allowDangerousCharacters||!1,quoteSmart:n.quoteSmart||!1,preferUnquoted:n.preferUnquoted||!1,tightAttributes:n.tightAttributes||!1,upperDoctype:n.upperDoctype||!1,tightDoctype:n.tightDoctype||!1,bogusComments:n.bogusComments||!1,tightCommaSeparatedLists:n.tightCommaSeparatedLists||!1,tightSelfClosing:n.tightSelfClosing||!1,collapseEmptyAttributes:n.collapseEmptyAttributes||!1,allowDangerousHtml:n.allowDangerousHtml||!1,voids:n.voids||U_,characterReferences:n.characterReferences||Zy,closeSelfClosing:n.closeSelfClosing||!1,closeEmptyElements:n.closeEmptyElements||!1},schema:n.space==="svg"?Kp:X_,quote:r,alternative:o}.one(Array.isArray(e)?{type:"root",children:e}:e,void 0,void 0)}function nv(e,t,n){return qy(e,t,n,this)}function rv(e){const t=[],n=e&&e.children||ev;let r=-1;for(;++r<n.length;)t[r]=this.one(n[r],r,e);return t.join("")}function ov(e){return Array.isArray(e)?e:[e]}function Pi(e,t=!1){var i;const n=e.split(/(\r?\n)/g);let r=0;const o=[];for(let l=0;l<n.length;l+=2){const s=t?n[l]+(n[l+1]||""):n[l];o.push([s,r]),r+=n[l].length,r+=((i=n[l+1])==null?void 0:i.length)||0}return o}function ga(e){return!e||["plaintext","txt","text","plain"].includes(e)}function Zp(e){return e==="ansi"||ga(e)}function _a(e){return e==="none"}function ef(e){return _a(e)}function tf(e,t){var r;if(!t)return e;e.properties||(e.properties={}),(r=e.properties).class||(r.class=[]),typeof e.properties.class=="string"&&(e.properties.class=e.properties.class.split(/\s+/g)),Array.isArray(e.properties.class)||(e.properties.class=[]);const n=Array.isArray(t)?t:t.split(/\s+/g);for(const o of n)o&&!e.properties.class.includes(o)&&e.properties.class.push(o);return e}function iv(e,t){let n=0;const r=[];for(const o of t)o>n&&r.push({...e,content:e.content.slice(n,o),offset:e.offset+n}),n=o;return n<e.content.length&&r.push({...e,content:e.content.slice(n),offset:e.offset+n}),r}function lv(e,t){const n=Array.from(t instanceof Set?t:new Set(t)).sort((r,o)=>r-o);return n.length?e.map(r=>r.flatMap(o=>{const i=n.filter(l=>o.offset<l&&l<o.offset+o.content.length).map(l=>l-o.offset).sort((l,s)=>l-s);return i.length?iv(o,i):o})):e}async function nf(e){return Promise.resolve(typeof e=="function"?e():e).then(t=>t.default||t)}function ri(e,t){const n=typeof e=="string"?{}:{...e.colorReplacements},r=typeof e=="string"?e:e.name;for(const[o,i]of Object.entries((t==null?void 0:t.colorReplacements)||{}))typeof i=="string"?n[o]=i:o===r&&Object.assign(n,i);return n}function Yt(e,t){return e&&((t==null?void 0:t[e==null?void 0:e.toLowerCase()])||e)}function rf(e){const t={};return e.color&&(t.color=e.color),e.bgColor&&(t["background-color"]=e.bgColor),e.fontStyle&&(e.fontStyle&dt.Italic&&(t["font-style"]="italic"),e.fontStyle&dt.Bold&&(t["font-weight"]="bold"),e.fontStyle&dt.Underline&&(t["text-decoration"]="underline")),t}function sv(e){return typeof e=="string"?e:Object.entries(e).map(([t,n])=>`${t}:${n}`).join(";")}function av(e){const t=Pi(e,!0).map(([o])=>o);function n(o){if(o===e.length)return{line:t.length-1,character:t[t.length-1].length};let i=o,l=0;for(const s of t){if(i<s.length)break;i-=s.length,l++}return{line:l,character:i}}function r(o,i){let l=0;for(let s=0;s<o;s++)l+=t[s].length;return l+=i,l}return{lines:t,indexToPos:n,posToIndex:r}}class ve extends Error{constructor(t){super(t),this.name="ShikiError"}}const of=new WeakMap;function Ri(e,t){of.set(e,t)}function Dr(e){return of.get(e)}class Fn{constructor(...t){x(this,"_stacks",{});x(this,"lang");if(t.length===2){const[n,r]=t;this.lang=r,this._stacks=n}else{const[n,r,o]=t;this.lang=r,this._stacks={[o]:n}}}get themes(){return Object.keys(this._stacks)}get theme(){return this.themes[0]}get _stack(){return this._stacks[this.theme]}static initial(t,n){return new Fn(Object.fromEntries(ov(n).map(r=>[r,us])),t)}getInternalStack(t=this.theme){return this._stacks[t]}get scopes(){return qu(this._stacks[this.theme])}getScopes(t=this.theme){return qu(this._stacks[t])}toJSON(){return{lang:this.lang,theme:this.theme,themes:this.themes,scopes:this.scopes}}}function qu(e){const t=[],n=new Set;function r(o){var l;if(n.has(o))return;n.add(o);const i=(l=o==null?void 0:o.nameScopesList)==null?void 0:l.scopeName;i&&t.push(i),o.parent&&r(o.parent)}return r(e),t}function uv(e,t){if(!(e instanceof Fn))throw new ve("Invalid grammar state");return e.getInternalStack(t)}function cv(){const e=new WeakMap;function t(n){if(!e.has(n.meta)){let r=function(l){if(typeof l=="number"){if(l<0||l>n.source.length)throw new ve(`Invalid decoration offset: ${l}. Code length: ${n.source.length}`);return{...o.indexToPos(l),offset:l}}else{const s=o.lines[l.line];if(s===void 0)throw new ve(`Invalid decoration position ${JSON.stringify(l)}. Lines length: ${o.lines.length}`);if(l.character<0||l.character>s.length)throw new ve(`Invalid decoration position ${JSON.stringify(l)}. Line ${l.line} length: ${s.length}`);return{...l,offset:o.posToIndex(l.line,l.character)}}};const o=av(n.source),i=(n.options.decorations||[]).map(l=>({...l,start:r(l.start),end:r(l.end)}));dv(i),e.set(n.meta,{decorations:i,converter:o,source:n.source})}return e.get(n.meta)}return{name:"shiki:decorations",tokens(n){var l;if(!((l=this.options.decorations)!=null&&l.length))return;const o=t(this).decorations.flatMap(s=>[s.start.offset,s.end.offset]);return lv(n,o)},code(n){var d;if(!((d=this.options.decorations)!=null&&d.length))return;const r=t(this),o=Array.from(n.children).filter(f=>f.type==="element"&&f.tagName==="span");if(o.length!==r.converter.lines.length)throw new ve(`Number of lines in code element (${o.length}) does not match the number of lines in the source (${r.converter.lines.length}). Failed to apply decorations.`);function i(f,m,g,v){const E=o[f];let w="",h=-1,c=-1;if(m===0&&(h=0),g===0&&(c=0),g===Number.POSITIVE_INFINITY&&(c=E.children.length),h===-1||c===-1)for(let S=0;S<E.children.length;S++)w+=lf(E.children[S]),h===-1&&w.length===m&&(h=S+1),c===-1&&w.length===g&&(c=S+1);if(h===-1)throw new ve(`Failed to find start index for decoration ${JSON.stringify(v.start)}`);if(c===-1)throw new ve(`Failed to find end index for decoration ${JSON.stringify(v.end)}`);const _=E.children.slice(h,c);if(!v.alwaysWrap&&_.length===E.children.length)s(E,v,"line");else if(!v.alwaysWrap&&_.length===1&&_[0].type==="element")s(_[0],v,"token");else{const S={type:"element",tagName:"span",properties:{},children:_};s(S,v,"wrapper"),E.children.splice(h,_.length,S)}}function l(f,m){o[f]=s(o[f],m,"line")}function s(f,m,g){var w;const v=m.properties||{},E=m.transform||(h=>h);return f.tagName=m.tagName||"span",f.properties={...f.properties,...v,class:f.properties.class},(w=m.properties)!=null&&w.class&&tf(f,m.properties.class),f=E(f,g)||f,f}const a=[],u=r.decorations.sort((f,m)=>m.start.offset-f.start.offset);for(const f of u){const{start:m,end:g}=f;if(m.line===g.line)i(m.line,m.character,g.character,f);else if(m.line<g.line){i(m.line,m.character,Number.POSITIVE_INFINITY,f);for(let v=m.line+1;v<g.line;v++)a.unshift(()=>l(v,f));i(g.line,0,g.character,f)}}a.forEach(f=>f())}}}function dv(e){for(let t=0;t<e.length;t++){const n=e[t];if(n.start.offset>n.end.offset)throw new ve(`Invalid decoration range: ${JSON.stringify(n.start)} - ${JSON.stringify(n.end)}`);for(let r=t+1;r<e.length;r++){const o=e[r],i=n.start.offset<o.start.offset&&o.start.offset<n.end.offset,l=n.start.offset<o.end.offset&&o.end.offset<n.end.offset,s=o.start.offset<n.start.offset&&n.start.offset<o.end.offset,a=o.start.offset<n.end.offset&&n.end.offset<o.end.offset;if(i||l||s||a){if(l&&l||s&&a)continue;throw new ve(`Decorations ${JSON.stringify(n.start)} and ${JSON.stringify(o.start)} intersect.`)}}}}function lf(e){return e.type==="text"?e.value:e.type==="element"?e.children.map(lf).join(""):""}const pv=[cv()];function oi(e){return[...e.transformers||[],...pv]}var Xt=["black","red","green","yellow","blue","magenta","cyan","white","brightBlack","brightRed","brightGreen","brightYellow","brightBlue","brightMagenta","brightCyan","brightWhite"],cl={1:"bold",2:"dim",3:"italic",4:"underline",7:"reverse",9:"strikethrough"};function fv(e,t){const n=e.indexOf("\x1B[",t);if(n!==-1){const r=e.indexOf("m",n);return{sequence:e.substring(n+2,r).split(";"),startPosition:n,position:r+1}}return{position:e.length}}function Yu(e,t){let n=1;const r=e[t+n++];let o;if(r==="2"){const i=[e[t+n++],e[t+n++],e[t+n]].map(l=>Number.parseInt(l));i.length===3&&!i.some(l=>Number.isNaN(l))&&(o={type:"rgb",rgb:i})}else if(r==="5"){const i=Number.parseInt(e[t+n]);Number.isNaN(i)||(o={type:"table",index:Number(i)})}return[n,o]}function mv(e){const t=[];for(let n=0;n<e.length;n++){const r=e[n],o=Number.parseInt(r);if(!Number.isNaN(o))if(o===0)t.push({type:"resetAll"});else if(o<=9)cl[o]&&t.push({type:"setDecoration",value:cl[o]});else if(o<=29){const i=cl[o-20];i&&t.push({type:"resetDecoration",value:i})}else if(o<=37)t.push({type:"setForegroundColor",value:{type:"named",name:Xt[o-30]}});else if(o===38){const[i,l]=Yu(e,n);l&&t.push({type:"setForegroundColor",value:l}),n+=i}else if(o===39)t.push({type:"resetForegroundColor"});else if(o<=47)t.push({type:"setBackgroundColor",value:{type:"named",name:Xt[o-40]}});else if(o===48){const[i,l]=Yu(e,n);l&&t.push({type:"setBackgroundColor",value:l}),n+=i}else o===49?t.push({type:"resetBackgroundColor"}):o>=90&&o<=97?t.push({type:"setForegroundColor",value:{type:"named",name:Xt[o-90+8]}}):o>=100&&o<=107&&t.push({type:"setBackgroundColor",value:{type:"named",name:Xt[o-100+8]}})}return t}function hv(){let e=null,t=null,n=new Set;return{parse(r){const o=[];let i=0;do{const l=fv(r,i),s=l.sequence?r.substring(i,l.startPosition):r.substring(i);if(s.length>0&&o.push({value:s,foreground:e,background:t,decorations:new Set(n)}),l.sequence){const a=mv(l.sequence);for(const u of a)u.type==="resetAll"?(e=null,t=null,n.clear()):u.type==="resetForegroundColor"?e=null:u.type==="resetBackgroundColor"?t=null:u.type==="resetDecoration"&&n.delete(u.value);for(const u of a)u.type==="setForegroundColor"?e=u.value:u.type==="setBackgroundColor"?t=u.value:u.type==="setDecoration"&&n.add(u.value)}i=l.position}while(i<r.length);return o}}}var gv={black:"#000000",red:"#bb0000",green:"#00bb00",yellow:"#bbbb00",blue:"#0000bb",magenta:"#ff00ff",cyan:"#00bbbb",white:"#eeeeee",brightBlack:"#555555",brightRed:"#ff5555",brightGreen:"#00ff00",brightYellow:"#ffff55",brightBlue:"#5555ff",brightMagenta:"#ff55ff",brightCyan:"#55ffff",brightWhite:"#ffffff"};function _v(e=gv){function t(s){return e[s]}function n(s){return`#${s.map(a=>Math.max(0,Math.min(a,255)).toString(16).padStart(2,"0")).join("")}`}let r;function o(){if(r)return r;r=[];for(let u=0;u<Xt.length;u++)r.push(t(Xt[u]));let s=[0,95,135,175,215,255];for(let u=0;u<6;u++)for(let d=0;d<6;d++)for(let f=0;f<6;f++)r.push(n([s[u],s[d],s[f]]));let a=8;for(let u=0;u<24;u++,a+=10)r.push(n([a,a,a]));return r}function i(s){return o()[s]}function l(s){switch(s.type){case"named":return t(s.name);case"rgb":return n(s.rgb);case"table":return i(s.index)}}return{value:l}}function yv(e,t,n){const r=ri(e,n),o=Pi(t),i=_v(Object.fromEntries(Xt.map(s=>{var a;return[s,(a=e.colors)==null?void 0:a[`terminal.ansi${s[0].toUpperCase()}${s.substring(1)}`]]}))),l=hv();return o.map(s=>l.parse(s[0]).map(a=>{let u,d;a.decorations.has("reverse")?(u=a.background?i.value(a.background):e.bg,d=a.foreground?i.value(a.foreground):e.fg):(u=a.foreground?i.value(a.foreground):e.fg,d=a.background?i.value(a.background):void 0),u=Yt(u,r),d=Yt(d,r),a.decorations.has("dim")&&(u=vv(u));let f=dt.None;return a.decorations.has("bold")&&(f|=dt.Bold),a.decorations.has("italic")&&(f|=dt.Italic),a.decorations.has("underline")&&(f|=dt.Underline),{content:a.value,offset:s[1],color:u,bgColor:d,fontStyle:f}}))}function vv(e){const t=e.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);if(t)if(t[3]){const r=Math.round(Number.parseInt(t[3],16)/2).toString(16).padStart(2,"0");return`#${t[1]}${t[2]}${r}`}else return t[2]?`#${t[1]}${t[2]}80`:`#${Array.from(t[1]).map(r=>`${r}${r}`).join("")}80`;const n=e.match(/var\((--[\w-]+-ansi-[\w-]+)\)/);return n?`var(${n[1]}-dim)`:e}function ya(e,t,n={}){const{lang:r="text",theme:o=e.getLoadedThemes()[0]}=n;if(ga(r)||_a(o))return Pi(t).map(a=>[{content:a[0],offset:a[1]}]);const{theme:i,colorMap:l}=e.setTheme(o);if(r==="ansi")return yv(i,t,n);const s=e.getLanguage(r);if(n.grammarState){if(n.grammarState.lang!==s.name)throw new ft(`Grammar state language "${n.grammarState.lang}" does not match highlight language "${s.name}"`);if(!n.grammarState.themes.includes(i.name))throw new ft(`Grammar state themes "${n.grammarState.themes}" do not contain highlight theme "${i.name}"`)}return Sv(t,s,i,l,n)}function Ev(...e){if(e.length===2)return Dr(e[1]);const[t,n,r={}]=e,{lang:o="text",theme:i=t.getLoadedThemes()[0]}=r;if(ga(o)||_a(i))throw new ft("Plain language does not have grammar state");if(o==="ansi")throw new ft("ANSI language does not have grammar state");const{theme:l,colorMap:s}=t.setTheme(i),a=t.getLanguage(o);return new Fn(ii(n,a,l,s,r).stateStack,a.name,l.name)}function Sv(e,t,n,r,o){const i=ii(e,t,n,r,o),l=new Fn(ii(e,t,n,r,o).stateStack,t.name,n.name);return Ri(i.tokens,l),i.tokens}function ii(e,t,n,r,o){const i=ri(n,o),{tokenizeMaxLineLength:l=0,tokenizeTimeLimit:s=500}=o,a=Pi(e);let u=o.grammarState?uv(o.grammarState,n.name)??us:o.grammarContextCode!=null?ii(o.grammarContextCode,t,n,r,{...o,grammarState:void 0,grammarContextCode:void 0}).stateStack:us,d=[];const f=[];for(let m=0,g=a.length;m<g;m++){const[v,E]=a[m];if(v===""){d=[],f.push([]);continue}if(l>0&&v.length>=l){d=[],f.push([{content:v,offset:E,color:"",fontStyle:0}]);continue}let w,h,c;o.includeExplanation&&(w=t.tokenizeLine(v,u),h=w.tokens,c=0);const _=t.tokenizeLine2(v,u,s),S=_.tokens.length/2;for(let C=0;C<S;C++){const L=_.tokens[2*C],R=C+1<S?_.tokens[2*C+2]:v.length;if(L===R)continue;const A=_.tokens[2*C+1],B=Yt(r[Vn.getForeground(A)],i),b=Vn.getFontStyle(A),_e={content:v.substring(L,R),offset:E+L,color:B,fontStyle:b};if(o.includeExplanation){const vt=[];if(o.includeExplanation!=="scopeName")for(const je of n.settings){let St;switch(typeof je.scope){case"string":St=je.scope.split(/,/).map(Ut=>Ut.trim());break;case"object":St=je.scope;break;default:continue}vt.push({settings:je,selectors:St.map(Ut=>Ut.split(/ /))})}_e.explanation=[];let Et=0;for(;L+Et<R;){const je=h[c],St=v.substring(je.startIndex,je.endIndex);Et+=St.length,_e.explanation.push({content:St,scopes:o.includeExplanation==="scopeName"?wv(je.scopes):xv(vt,je.scopes)}),c+=1}}d.push(_e)}f.push(d),d=[],u=_.ruleStack}return{tokens:f,stateStack:u}}function wv(e){return e.map(t=>({scopeName:t}))}function xv(e,t){const n=[];for(let r=0,o=t.length;r<o;r++){const i=t[r];n[r]={scopeName:i,themeMatches:Cv(e,i,t.slice(0,r))}}return n}function Xu(e,t){return e===t||t.substring(0,e.length)===e&&t[e.length]==="."}function kv(e,t,n){if(!Xu(e[e.length-1],t))return!1;let r=e.length-2,o=n.length-1;for(;r>=0&&o>=0;)Xu(e[r],n[o])&&(r-=1),o-=1;return r===-1}function Cv(e,t,n){const r=[];for(const{selectors:o,settings:i}of e)for(const l of o)if(kv(l,t,n)){r.push(i);break}return r}function sf(e,t,n){const r=Object.entries(n.themes).filter(a=>a[1]).map(a=>({color:a[0],theme:a[1]})),o=r.map(a=>{const u=ya(e,t,{...n,theme:a.theme}),d=Dr(u),f=typeof a.theme=="string"?a.theme:a.theme.name;return{tokens:u,state:d,theme:f}}),i=Pv(...o.map(a=>a.tokens)),l=i[0].map((a,u)=>a.map((d,f)=>{const m={content:d.content,variants:{},offset:d.offset};return"includeExplanation"in n&&n.includeExplanation&&(m.explanation=d.explanation),i.forEach((g,v)=>{const{content:E,explanation:w,offset:h,...c}=g[u][f];m.variants[r[v].color]=c}),m})),s=o[0].state?new Fn(Object.fromEntries(o.map(a=>{var u;return[a.theme,(u=a.state)==null?void 0:u.getInternalStack(a.theme)]})),o[0].state.lang):void 0;return s&&Ri(l,s),l}function Pv(...e){const t=e.map(()=>[]),n=e.length;for(let r=0;r<e[0].length;r++){const o=e.map(a=>a[r]),i=t.map(()=>[]);t.forEach((a,u)=>a.push(i[u]));const l=o.map(()=>0),s=o.map(a=>a[0]);for(;s.every(a=>a);){const a=Math.min(...s.map(u=>u.content.length));for(let u=0;u<n;u++){const d=s[u];d.content.length===a?(i[u].push(d),l[u]+=1,s[u]=o[u][l[u]]):(i[u].push({...d,content:d.content.slice(0,a)}),s[u]={...d,content:d.content.slice(a),offset:d.offset+a})}}}return t}function li(e,t,n){let r,o,i,l,s,a;if("themes"in n){const{defaultColor:u="light",cssVariablePrefix:d="--shiki-"}=n,f=Object.entries(n.themes).filter(w=>w[1]).map(w=>({color:w[0],theme:w[1]})).sort((w,h)=>w.color===u?-1:h.color===u?1:0);if(f.length===0)throw new ft("`themes` option must not be empty");const m=sf(e,t,n);if(a=Dr(m),u&&!f.find(w=>w.color===u))throw new ft(`\`themes\` option must contain the defaultColor key \`${u}\``);const g=f.map(w=>e.getTheme(w.theme)),v=f.map(w=>w.color);i=m.map(w=>w.map(h=>Rv(h,v,d,u))),a&&Ri(i,a);const E=f.map(w=>ri(w.theme,n));o=f.map((w,h)=>(h===0&&u?"":`${d+w.color}:`)+(Yt(g[h].fg,E[h])||"inherit")).join(";"),r=f.map((w,h)=>(h===0&&u?"":`${d+w.color}-bg:`)+(Yt(g[h].bg,E[h])||"inherit")).join(";"),l=`shiki-themes ${g.map(w=>w.name).join(" ")}`,s=u?void 0:[o,r].join(";")}else if("theme"in n){const u=ri(n.theme,n);i=ya(e,t,n);const d=e.getTheme(n.theme);r=Yt(d.bg,u),o=Yt(d.fg,u),l=d.name,a=Dr(i)}else throw new ft("Invalid options, either `theme` or `themes` must be provided");return{tokens:i,fg:o,bg:r,themeName:l,rootStyle:s,grammarState:a}}function Rv(e,t,n,r){const o={content:e.content,explanation:e.explanation,offset:e.offset},i=t.map(a=>rf(e.variants[a])),l=new Set(i.flatMap(a=>Object.keys(a))),s={};return i.forEach((a,u)=>{for(const d of l){const f=a[d]||"inherit";if(u===0&&r)s[d]=f;else{const m=d==="color"?"":d==="background-color"?"-bg":`-${d}`,g=n+t[u]+(d==="color"?"":m);s[g]=f}}}),o.htmlStyle=s,o}function si(e,t,n,r={meta:{},options:n,codeToHast:(o,i)=>si(e,o,i),codeToTokens:(o,i)=>li(e,o,i)}){var g,v;let o=t;for(const E of oi(n))o=((g=E.preprocess)==null?void 0:g.call(r,o,n))||o;let{tokens:i,fg:l,bg:s,themeName:a,rootStyle:u,grammarState:d}=li(e,o,n);const{mergeWhitespaces:f=!0}=n;f===!0?i=Tv(i):f==="never"&&(i=Iv(i));const m={...r,get source(){return o}};for(const E of oi(n))i=((v=E.tokens)==null?void 0:v.call(m,i))||i;return Lv(i,{...n,fg:l,bg:s,themeName:a,rootStyle:u},m,d)}function Lv(e,t,n,r=Dr(e)){var v,E,w;const o=oi(t),i=[],l={type:"root",children:[]},{structure:s="classic",tabindex:a="0"}=t;let u={type:"element",tagName:"pre",properties:{class:`shiki ${t.themeName||""}`,style:t.rootStyle||`background-color:${t.bg};color:${t.fg}`,...a!==!1&&a!=null?{tabindex:a.toString()}:{},...Object.fromEntries(Array.from(Object.entries(t.meta||{})).filter(([h])=>!h.startsWith("_")))},children:[]},d={type:"element",tagName:"code",properties:{},children:i};const f=[],m={...n,structure:s,addClassToHast:tf,get source(){return n.source},get tokens(){return e},get options(){return t},get root(){return l},get pre(){return u},get code(){return d},get lines(){return f}};if(e.forEach((h,c)=>{var C,L;c&&(s==="inline"?l.children.push({type:"element",tagName:"br",properties:{},children:[]}):s==="classic"&&i.push({type:"text",value:`
`}));let _={type:"element",tagName:"span",properties:{class:"line"},children:[]},S=0;for(const R of h){let A={type:"element",tagName:"span",properties:{...R.htmlAttrs},children:[{type:"text",value:R.content}]};R.htmlStyle;const B=sv(R.htmlStyle||rf(R));B&&(A.properties.style=B);for(const b of o)A=((C=b==null?void 0:b.span)==null?void 0:C.call(m,A,c+1,S,_,R))||A;s==="inline"?l.children.push(A):s==="classic"&&_.children.push(A),S+=R.content.length}if(s==="classic"){for(const R of o)_=((L=R==null?void 0:R.line)==null?void 0:L.call(m,_,c+1))||_;f.push(_),i.push(_)}}),s==="classic"){for(const h of o)d=((v=h==null?void 0:h.code)==null?void 0:v.call(m,d))||d;u.children.push(d);for(const h of o)u=((E=h==null?void 0:h.pre)==null?void 0:E.call(m,u))||u;l.children.push(u)}let g=l;for(const h of o)g=((w=h==null?void 0:h.root)==null?void 0:w.call(m,g))||g;return r&&Ri(g,r),g}function Tv(e){return e.map(t=>{const n=[];let r="",o=0;return t.forEach((i,l)=>{const a=!(i.fontStyle&&i.fontStyle&dt.Underline);a&&i.content.match(/^\s+$/)&&t[l+1]?(o||(o=i.offset),r+=i.content):r?(a?n.push({...i,offset:o,content:r+i.content}):n.push({content:r,offset:o},i),o=0,r=""):n.push(i)}),n})}function Iv(e){return e.map(t=>t.flatMap(n=>{if(n.content.match(/^\s+$/))return n;const r=n.content.match(/^(\s*)(.*?)(\s*)$/);if(!r)return n;const[,o,i,l]=r;if(!o&&!l)return n;const s=[{...n,offset:n.offset+o.length,content:i}];return o&&s.unshift({content:o,offset:n.offset}),l&&s.push({content:l,offset:n.offset+o.length+i.length}),s}))}function Av(e,t,n){var i;const r={meta:{},options:n,codeToHast:(l,s)=>si(e,l,s),codeToTokens:(l,s)=>li(e,l,s)};let o=tv(si(e,t,n,r));for(const l of oi(n))o=((i=l.postprocess)==null?void 0:i.call(r,o,n))||o;return o}const Ju={light:"#333333",dark:"#bbbbbb"},Zu={light:"#fffffe",dark:"#1e1e1e"},ec="__shiki_resolved";function va(e){var s,a,u,d,f;if(e!=null&&e[ec])return e;const t={...e};t.tokenColors&&!t.settings&&(t.settings=t.tokenColors,delete t.tokenColors),t.type||(t.type="dark"),t.colorReplacements={...t.colorReplacements},t.settings||(t.settings=[]);let{bg:n,fg:r}=t;if(!n||!r){const m=t.settings?t.settings.find(g=>!g.name&&!g.scope):void 0;(s=m==null?void 0:m.settings)!=null&&s.foreground&&(r=m.settings.foreground),(a=m==null?void 0:m.settings)!=null&&a.background&&(n=m.settings.background),!r&&((u=t==null?void 0:t.colors)!=null&&u["editor.foreground"])&&(r=t.colors["editor.foreground"]),!n&&((d=t==null?void 0:t.colors)!=null&&d["editor.background"])&&(n=t.colors["editor.background"]),r||(r=t.type==="light"?Ju.light:Ju.dark),n||(n=t.type==="light"?Zu.light:Zu.dark),t.fg=r,t.bg=n}t.settings[0]&&t.settings[0].settings&&!t.settings[0].scope||t.settings.unshift({settings:{foreground:t.fg,background:t.bg}});let o=0;const i=new Map;function l(m){var v;if(i.has(m))return i.get(m);o+=1;const g=`#${o.toString(16).padStart(8,"0").toLowerCase()}`;return(v=t.colorReplacements)!=null&&v[`#${g}`]?l(m):(i.set(m,g),g)}t.settings=t.settings.map(m=>{var w,h;const g=((w=m.settings)==null?void 0:w.foreground)&&!m.settings.foreground.startsWith("#"),v=((h=m.settings)==null?void 0:h.background)&&!m.settings.background.startsWith("#");if(!g&&!v)return m;const E={...m,settings:{...m.settings}};if(g){const c=l(m.settings.foreground);t.colorReplacements[c]=m.settings.foreground,E.settings.foreground=c}if(v){const c=l(m.settings.background);t.colorReplacements[c]=m.settings.background,E.settings.background=c}return E});for(const m of Object.keys(t.colors||{}))if((m==="editor.foreground"||m==="editor.background"||m.startsWith("terminal.ansi"))&&!((f=t.colors[m])!=null&&f.startsWith("#"))){const g=l(t.colors[m]);t.colorReplacements[g]=t.colors[m],t.colors[m]=g}return Object.defineProperty(t,ec,{enumerable:!1,writable:!1,value:!0}),t}async function af(e){return Array.from(new Set((await Promise.all(e.filter(t=>!Zp(t)).map(async t=>await nf(t).then(n=>Array.isArray(n)?n:[n])))).flat()))}async function uf(e){return(await Promise.all(e.map(async n=>ef(n)?null:va(await nf(n))))).filter(n=>!!n)}class Nv extends $_{constructor(n,r,o,i={}){super(n);x(this,"_resolvedThemes",new Map);x(this,"_resolvedGrammars",new Map);x(this,"_langMap",new Map);x(this,"_langGraph",new Map);x(this,"_textmateThemeCache",new WeakMap);x(this,"_loadedThemesCache",null);x(this,"_loadedLanguagesCache",null);this._resolver=n,this._themes=r,this._langs=o,this._alias=i,this._themes.map(l=>this.loadTheme(l)),this.loadLanguages(this._langs)}getTheme(n){return typeof n=="string"?this._resolvedThemes.get(n):this.loadTheme(n)}loadTheme(n){const r=va(n);return r.name&&(this._resolvedThemes.set(r.name,r),this._loadedThemesCache=null),r}getLoadedThemes(){return this._loadedThemesCache||(this._loadedThemesCache=[...this._resolvedThemes.keys()]),this._loadedThemesCache}setTheme(n){let r=this._textmateThemeCache.get(n);r||(r=Jo.createFromRawTheme(n),this._textmateThemeCache.set(n,r)),this._syncRegistry.setTheme(r)}getGrammar(n){if(this._alias[n]){const r=new Set([n]);for(;this._alias[n];){if(n=this._alias[n],r.has(n))throw new ve(`Circular alias \`${Array.from(r).join(" -> ")} -> ${n}\``);r.add(n)}}return this._resolvedGrammars.get(n)}loadLanguage(n){var l,s,a,u;if(this.getGrammar(n.name))return;const r=new Set([...this._langMap.values()].filter(d=>{var f;return(f=d.embeddedLangsLazy)==null?void 0:f.includes(n.name)}));this._resolver.addLanguage(n);const o={balancedBracketSelectors:n.balancedBracketSelectors||["*"],unbalancedBracketSelectors:n.unbalancedBracketSelectors||[]};this._syncRegistry._rawGrammars.set(n.scopeName,n);const i=this.loadGrammarWithConfiguration(n.scopeName,1,o);if(i.name=n.name,this._resolvedGrammars.set(n.name,i),n.aliases&&n.aliases.forEach(d=>{this._alias[d]=n.name}),this._loadedLanguagesCache=null,r.size)for(const d of r)this._resolvedGrammars.delete(d.name),this._loadedLanguagesCache=null,(s=(l=this._syncRegistry)==null?void 0:l._injectionGrammars)==null||s.delete(d.scopeName),(u=(a=this._syncRegistry)==null?void 0:a._grammars)==null||u.delete(d.scopeName),this.loadLanguage(this._langMap.get(d.name))}dispose(){super.dispose(),this._resolvedThemes.clear(),this._resolvedGrammars.clear(),this._langMap.clear(),this._langGraph.clear(),this._loadedThemesCache=null}loadLanguages(n){for(const i of n)this.resolveEmbeddedLanguages(i);const r=Array.from(this._langGraph.entries()),o=r.filter(([i,l])=>!l);if(o.length){const i=r.filter(([l,s])=>{var a;return s&&((a=s.embeddedLangs)==null?void 0:a.some(u=>o.map(([d])=>d).includes(u)))}).filter(l=>!o.includes(l));throw new ve(`Missing languages ${o.map(([l])=>`\`${l}\``).join(", ")}, required by ${i.map(([l])=>`\`${l}\``).join(", ")}`)}for(const[i,l]of r)this._resolver.addLanguage(l);for(const[i,l]of r)this.loadLanguage(l)}getLoadedLanguages(){return this._loadedLanguagesCache||(this._loadedLanguagesCache=[...new Set([...this._resolvedGrammars.keys(),...Object.keys(this._alias)])]),this._loadedLanguagesCache}resolveEmbeddedLanguages(n){if(this._langMap.set(n.name,n),this._langGraph.set(n.name,n),n.embeddedLangs)for(const r of n.embeddedLangs)this._langGraph.set(r,this._langMap.get(r))}}class bv{constructor(t,n){x(this,"_langs",new Map);x(this,"_scopeToLang",new Map);x(this,"_injections",new Map);x(this,"_onigLib");this._onigLib={createOnigScanner:r=>t.createScanner(r),createOnigString:r=>t.createString(r)},n.forEach(r=>this.addLanguage(r))}get onigLib(){return this._onigLib}getLangRegistration(t){return this._langs.get(t)}loadGrammar(t){return this._scopeToLang.get(t)}addLanguage(t){this._langs.set(t.name,t),t.aliases&&t.aliases.forEach(n=>{this._langs.set(n,t)}),this._scopeToLang.set(t.scopeName,t),t.injectTo&&t.injectTo.forEach(n=>{this._injections.get(n)||this._injections.set(n,[]),this._injections.get(n).push(t.scopeName)})}getInjections(t){const n=t.split(".");let r=[];for(let o=1;o<=n.length;o++){const i=n.slice(0,o).join(".");r=[...r,...this._injections.get(i)||[]]}return r}}let Jn=0;function Ov(e){Jn+=1,e.warnings!==!1&&Jn>=10&&Jn%10===0&&console.warn(`[Shiki] ${Jn} instances have been created. Shiki is supposed to be used as a singleton, consider refactoring your code to cache your highlighter instance; Or call \`highlighter.dispose()\` to release unused instances.`);let t=!1;if(!e.engine)throw new ve("`engine` option is required for synchronous mode");const n=(e.langs||[]).flat(1),r=(e.themes||[]).flat(1).map(va),o=new bv(e.engine,n),i=new Nv(o,r,n,e.langAlias);let l;function s(c){w();const _=i.getGrammar(typeof c=="string"?c:c.name);if(!_)throw new ve(`Language \`${c}\` not found, you may need to load it first`);return _}function a(c){if(c==="none")return{bg:"",fg:"",name:"none",settings:[],type:"dark"};w();const _=i.getTheme(c);if(!_)throw new ve(`Theme \`${c}\` not found, you may need to load it first`);return _}function u(c){w();const _=a(c);l!==c&&(i.setTheme(_),l=c);const S=i.getColorMap();return{theme:_,colorMap:S}}function d(){return w(),i.getLoadedThemes()}function f(){return w(),i.getLoadedLanguages()}function m(...c){w(),i.loadLanguages(c.flat(1))}async function g(...c){return m(await af(c))}function v(...c){w();for(const _ of c.flat(1))i.loadTheme(_)}async function E(...c){return w(),v(await uf(c))}function w(){if(t)throw new ve("Shiki instance has been disposed")}function h(){t||(t=!0,i.dispose(),Jn-=1)}return{setTheme:u,getTheme:a,getLanguage:s,getLoadedThemes:d,getLoadedLanguages:f,loadLanguage:g,loadLanguageSync:m,loadTheme:E,loadThemeSync:v,dispose:h,[Symbol.dispose]:h}}async function Dv(e={}){e.loadWasm;const[t,n,r]=await Promise.all([uf(e.themes||[]),af(e.langs||[]),e.engine||kp(e.loadWasm||t_())]);return Ov({...e,themes:t,langs:n,engine:r})}async function jv(e={}){const t=await Dv(e);return{getLastGrammarState:(...n)=>Ev(t,...n),codeToTokensBase:(n,r)=>ya(t,n,r),codeToTokensWithThemes:(n,r)=>sf(t,n,r),codeToTokens:(n,r)=>li(t,n,r),codeToHast:(n,r)=>si(t,n,r),codeToHtml:(n,r)=>Av(t,n,r),...t,getInternalContext:()=>t}}function Mv(e,t,n){let r,o,i;{const s=e;r=s.langs,o=s.themes,i=s.engine}async function l(s){function a(g){if(typeof g=="string"){if(Zp(g))return[];const v=r[g];if(!v)throw new ft(`Language \`${g}\` is not included in this bundle. You may want to load it from external source.`);return v}return g}function u(g){if(ef(g))return"none";if(typeof g=="string"){const v=o[g];if(!v)throw new ft(`Theme \`${g}\` is not included in this bundle. You may want to load it from external source.`);return v}return g}const d=(s.themes??[]).map(g=>u(g)),f=(s.langs??[]).map(g=>a(g)),m=await jv({engine:s.engine??i(),...s,themes:d,langs:f});return{...m,loadLanguage(...g){return m.loadLanguage(...g.map(a))},loadTheme(...g){return m.loadTheme(...g.map(u))}}}return l}const Vv=Mv({langs:Dg,themes:Mg,engine:()=>kp(p(()=>import("./assets/wasm-CG6Dc4jp.js"),[],import.meta.url))}),zv=({content:e})=>{const[t,n]=N.useState(null);if(N.useEffect(()=>{Vv({themes:["github-dark"],langs:["javascript","typescript","python","bash","html","css"]}).then(n)},[]),!t)return y.jsx("pre",{className:"whitespace-pre-wrap",children:e});const r=e.split(/(```[\s\S]*?```)/g);return y.jsx(y.Fragment,{children:r.map((o,i)=>{if(o.startsWith("```")){const l=o.split(`
`),s=l[0].replace("```","").trim(),a=l.slice(1,-1).join(`
`);return y.jsxs("div",{className:"relative",children:[y.jsxs("div",{className:"absolute top-1 right-1 flex gap-1",children:[y.jsx("button",{title:"Copy",onClick:()=>navigator.clipboard.writeText(a),className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:y.jsx(Sg,{size:14})}),y.jsx("button",{title:"Save",className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:y.jsx(wg,{size:14})})]}),y.jsx("div",{dangerouslySetInnerHTML:{__html:t.codeToHtml(a,{lang:s,theme:"github-dark"})}})]},i)}return y.jsx("div",{children:o},i)})})},Bv=({message:e})=>{const t=e.role==="user";return y.jsx("div",{className:`flex gap-3 ${t?"justify-end":"justify-start"}`,children:y.jsx("div",{className:`max-w-[85%] rounded-lg px-3 py-2 text-sm leading-relaxed ${t?"bg-adobe-bg-tertiary text-adobe-text-primary":"text-adobe-text-primary"}`,children:y.jsx(zv,{content:e.content})})})},$v=()=>{const{messages:e,isLoading:t}=wp(),n=N.useRef(null),r=N.useRef(null),[o,i]=N.useState(!1),l=N.useRef();N.useEffect(()=>{var a;(a=n.current)==null||a.scrollIntoView({behavior:"smooth"})},[e,t]),N.useEffect(()=>{const a=r.current;if(!a)return;const u=()=>{clearTimeout(l.current);const{scrollTop:d,scrollHeight:f,clientHeight:m}=a,g=f-(d+m)<100;i(!g),l.current=setTimeout(()=>{i(!1)},2e3)};return a.addEventListener("scroll",u),()=>{a.removeEventListener("scroll",u),clearTimeout(l.current)}},[]);const s=()=>{var a;(a=n.current)==null||a.scrollIntoView({behavior:"smooth"})};return y.jsxs("div",{ref:r,className:`flex-1 overflow-y-auto px-3 py-2 space-y-4
                scrollbar-thin scrollbar-thumb-adobe-border/50 scrollbar-track-adobe-bg-secondary/30
                hover:scrollbar-thumb-adobe-border/80 transition-[scrollbar-thumb] duration-300
                relative bg-adobe-bg-tertiary`,children:[e.length===0&&y.jsx("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary",children:y.jsx("span",{className:"text-sm",children:"Ask anything…"})}),e.map(a=>y.jsx(Bv,{message:a},a.id)),t&&y.jsx("div",{className:"flex items-center gap-2 text-adobe-text-secondary text-sm",children:y.jsx("span",{children:"AI is thinking…"})}),y.jsx("div",{ref:n}),o&&y.jsx("button",{onClick:s,className:"absolute right-4 bottom-4 p-2 rounded-full bg-adobe-bg-tertiary border border-adobe-border text-adobe-text-primary hover:bg-adobe-bg-secondary transition-all duration-300 shadow-md","aria-label":"Scroll to bottom",children:y.jsx(Eg,{size:18})})]})},Uv=ys.memo(({onAttachFile:e,onVoiceInput:t,onContextReference:n})=>{const[r,o]=N.useState(""),[i,l]=N.useState(!1),s=N.useRef(null),{addMessage:a,isLoading:u,setLoading:d,currentSession:f,createNewSession:m}=wp(),g=4e3,v=!r.trim(),E=r.length>g*.9;N.useEffect(()=>{const S=s.current;S&&(S.style.height="72px")},[]);const w=N.useCallback(()=>{const S=s.current;S&&(S.style.height="auto",S.style.height=`${Math.min(Math.max(S.scrollHeight,72),200)}px`)},[]),h=N.useCallback(S=>{o(S.target.value),w()},[w]),c=N.useCallback(async()=>{const S=r.trim();if(!(!S||u)){o(""),s.current&&(s.current.style.height="72px");try{d(!0),f||m(),a({content:S,role:"user"}),setTimeout(()=>{a({content:`Echo: ${S}`,role:"assistant"}),d(!1)},1e3)}catch{o(S),d(!1)}}},[r,u,f,a,d,m]),_=N.useCallback(S=>{S.key==="Enter"&&!S.shiftKey&&!i&&(S.preventDefault(),c())},[c,i]);return y.jsxs("div",{className:"px-4 pb-3 pt-2 bg-adobe-bg-secondary border-t border-adobe-border",children:[y.jsxs("div",{className:"relative flex items-end bg-adobe-bg-tertiary rounded-lg border border-adobe-border focus-within:border-adobe-accent focus-within:ring-1 focus-within:ring-adobe-accent transition-colors",children:[y.jsx("div",{className:"flex items-center pl-2 pb-1 self-end",children:y.jsx("button",{onClick:e,className:"text-adobe-text-secondary hover:text-adobe-accent transition p-1 rounded",title:"Attach file",disabled:u,children:y.jsx(Pg,{size:18})})}),y.jsx("textarea",{ref:s,rows:3,maxLength:g,value:r,onChange:h,onKeyDown:_,onCompositionStart:()=>l(!0),onCompositionEnd:()=>l(!1),placeholder:"Type a message…",className:`flex-1 resize-none bg-transparent text-adobe-text-primary text-sm p-2 outline-none placeholder:text-adobe-text-secondary/80
            min-h-[72px] max-h-[200px] leading-relaxed overflow-hidden`}),y.jsxs("div",{className:"flex items-center pr-2 pb-1 space-x-1 self-end",children:[y.jsx("button",{onClick:t,className:"text-adobe-text-secondary hover:text-adobe-warning transition p-1 rounded disabled:opacity-40",title:"Voice input",disabled:u,children:y.jsx(Cg,{size:18})}),y.jsx("button",{onClick:c,disabled:v||u,className:"text-adobe-accent hover:text-adobe-accent-hover transition p-1 rounded disabled:text-adobe-text-secondary/50 disabled:hover:text-adobe-text-secondary/50",title:"Send",children:u?y.jsx(kg,{size:18,className:"animate-spin"}):y.jsx(Lg,{size:18})})]})]}),y.jsxs("div",{className:"flex justify-between items-center mt-1 px-1",children:[y.jsxs("span",{className:`text-xs ${E?"text-adobe-warning":"text-adobe-text-secondary"}`,children:[r.length,"/",g]}),y.jsx("span",{className:"text-xs text-adobe-text-secondary",children:"Enter to send, Shift+Enter for new line"})]})]})}),Fv=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),[o,i]=N.useState([]),l=async()=>{const a=await(await fetch("https://api.openai.com/v1/models",{headers:{Authorization:`Bearer ${n}`},mode:"no-cors"})).json();i(a.data.map(u=>({id:u.id,name:u.id})))};return N.useEffect(()=>{n&&l()},[n]),y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:s=>t("openai",n,s.target.value),children:o.map(s=>y.jsx("option",{value:s.id,children:s.name},s.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"password",placeholder:"sk-...",value:n,onChange:s=>r(s.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("openai",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},Gv=/Mac/.test(navigator.platform),tc="http://localhost:11434",Hv=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(tc),[o,i]=N.useState([]),l=async()=>{try{const a=await(await fetch(`${n}/api/tags`,{mode:"cors"})).json();i(a.models.map(u=>({id:u.name,name:u.name})))}catch{i([])}};return N.useEffect(()=>{l()},[n]),y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),o.length?y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:s=>t("ollama",n,s.target.value),children:o.map(s=>y.jsx("option",{value:s.id,children:s.name},s.id))}):y.jsxs("p",{className:"text-xs text-adobe-text-secondary",children:["No models found at ",n]})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"text",placeholder:tc,value:n,onChange:s=>r(s.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("ollama",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},Wv=/Mac/.test(navigator.platform),nc="http://localhost:1234",Kv=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(nc),[o,i]=N.useState([]),l=async()=>{try{const a=await(await fetch(`${n}/v1/models`,{mode:"cors"})).json();i(a.data.map(u=>({id:u.id,name:u.id})))}catch{i([])}};return N.useEffect(()=>{l()},[n]),y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),o.length?y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:s=>t("lmstudio",n,s.target.value),children:o.map(s=>y.jsx("option",{value:s.id,children:s.name},s.id))}):y.jsxs("p",{className:"text-xs text-adobe-text-secondary",children:["No models found at ",n]})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"text",placeholder:nc,value:n,onChange:s=>r(s.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("lmstudio",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},Qv=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),[o]=N.useState([{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet"},{id:"claude-3-opus-20240229",name:"Claude 3 Opus"},{id:"claude-3-haiku-20240307",name:"Claude 3 Haiku"}]);return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:i=>t("anthropic",n,i.target.value),children:o.map(i=>y.jsx("option",{value:i.id,children:i.name},i.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"password",placeholder:"sk-ant-...",value:n,onChange:i=>r(i.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("anthropic",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},qv=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),o=[{id:"deepseek-chat",name:"DeepSeek Chat"},{id:"deepseek-coder",name:"DeepSeek Coder"}];return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:i=>t("deepseek",n,i.target.value),children:o.map(i=>y.jsx("option",{value:i.id,children:i.name},i.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"password",placeholder:"sk-...",value:n,onChange:i=>r(i.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("deepseek",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},Yv=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),o=[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro"},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash"}];return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:i=>t("gemini",n,i.target.value),children:o.map(i=>y.jsx("option",{value:i.id,children:i.name},i.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"password",placeholder:"AIza...",value:n,onChange:i=>r(i.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("gemini",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},Xv=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),o=[{id:"llama3-70b-8192",name:"LLaMA 3 70b"},{id:"mixtral-8x7b-32768",name:"Mixtral 8x7b"}];return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:i=>t("groq",n,i.target.value),children:o.map(i=>y.jsx("option",{value:i.id,children:i.name},i.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"password",placeholder:"gsk_...",value:n,onChange:i=>r(i.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("groq",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},Jv=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),o=[{id:"mistral-large-latest",name:"Mistral Large"},{id:"mistral-medium-latest",name:"Mistral Medium"}];return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:i=>t("mistral",n,i.target.value),children:o.map(i=>y.jsx("option",{value:i.id,children:i.name},i.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"password",placeholder:"...",value:n,onChange:i=>r(i.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("mistral",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},Zv=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),o=[{id:"moonshot-v1-8k",name:"Kimi 8k"},{id:"moonshot-v1-32k",name:"Kimi 32k"}];return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:i=>t("moonshot",n,i.target.value),children:o.map(i=>y.jsx("option",{value:i.id,children:i.name},i.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"password",placeholder:"sk-...",value:n,onChange:i=>r(i.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("moonshot",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},e0=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),o=[{id:"openai/gpt-4",name:"GPT-4 via OR"},{id:"anthropic/claude-3-5-sonnet",name:"Claude 3.5 via OR"}];return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:i=>t("openrouter",n,i.target.value),children:o.map(i=>y.jsx("option",{value:i.id,children:i.name},i.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"password",placeholder:"sk-or-...",value:n,onChange:i=>r(i.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("openrouter",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},t0=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),o=[{id:"llama-3-sonar-large-32k-online",name:"Sonar Large Online"},{id:"llama-3-sonar-small-32k-online",name:"Sonar Small Online"}];return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:i=>t("perplexity",n,i.target.value),children:o.map(i=>y.jsx("option",{value:i.id,children:i.name},i.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"password",placeholder:"pplx-...",value:n,onChange:i=>r(i.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("perplexity",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},n0=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),[o,i]=N.useState("china"),l=[{id:"qwen-max",name:"Qwen Max"},{id:"qwen-turbo",name:"Qwen Turbo"}];return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:s=>t("qwen",n,s.target.value),children:l.map(s=>y.jsx("option",{value:s.id,children:s.name},s.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsxs("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1 mb-2",value:o,onChange:s=>i(s.target.value),children:[y.jsx("option",{value:"china",children:"China API"}),y.jsx("option",{value:"international",children:"International API"})]}),y.jsx("input",{type:"password",placeholder:"sk-...",value:n,onChange:s=>r(s.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("qwen",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},r0=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),o=[{id:"meta-llama/Llama-3.2-70B-Instruct-Turbo",name:"Llama 3.2 70B Turbo"},{id:"mistralai/Mixtral-8x7B-Instruct-v0.1",name:"Mixtral 8x7B Instruct"},{id:"Qwen/Qwen2.5-Coder-32B-Instruct",name:"Qwen2.5 Coder 32B"}];return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:i=>t("together",n,i.target.value),children:o.map(i=>y.jsx("option",{value:i.id,children:i.name},i.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"password",placeholder:"...",value:n,onChange:i=>r(i.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("together",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},o0=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),[o,i]=N.useState("us-central1"),l=[{id:"claude-3-5-sonnet@20241022",name:"Claude 3.5 Sonnet"},{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro"},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash"}];return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:s=>t("vertex",n,s.target.value),children:l.map(s=>y.jsx("option",{value:s.id,children:s.name},s.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"text",placeholder:"GCP Project ID",value:n,onChange:s=>r(s.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"}),y.jsxs("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1 mt-2",value:o,onChange:s=>i(s.target.value),children:[y.jsx("option",{value:"us-central1",children:"us-central1"}),y.jsx("option",{value:"europe-west1",children:"europe-west1"}),y.jsx("option",{value:"asia-southeast1",children:"asia-southeast1"})]})]}),y.jsx("button",{onClick:()=>{t("vertex",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},i0=({onClose:e})=>{const{updateProviderKey:t}=ae(),[n,r]=N.useState(""),o=[{id:"grok-2-latest",name:"Grok-2"},{id:"grok-2-mini-latest",name:"Grok-2 Mini"}];return y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Model"}),y.jsx("select",{className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1",onChange:i=>t("xai",n,i.target.value),children:o.map(i=>y.jsx("option",{value:i.id,children:i.name},i.id))})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Input API Key"}),y.jsx("input",{type:"password",placeholder:"xai-...",value:n,onChange:i=>r(i.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"})]}),y.jsx("button",{onClick:()=>{t("xai",n),e()},className:"w-full bg-adobe-accent text-white rounded py-1",children:"Save & Close"})]})},l0={openai:Fv,anthropic:Qv,gemini:Yv,groq:Xv,deepseek:qv,mistral:Jv,moonshot:Zv,openrouter:e0,perplexity:t0,qwen:n0,together:r0,vertex:o0,xai:i0,ollama:Hv,lmstudio:Kv},s0=()=>{const{activeProvider:e,setProvider:t}=ae(),{closeModal:n}=ca(),r=l0[e],o=i=>{t(i.target.value)};return y.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:y.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-md w-[380px] max-h-[550px] overflow-y-auto p-4",children:[y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Select Provider"}),y.jsxs("select",{value:e,onChange:o,className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none",children:[y.jsx("option",{value:"openai",children:"OpenAI"}),y.jsx("option",{value:"anthropic",children:"Anthropic"}),y.jsx("option",{value:"gemini",children:"Google Gemini"}),y.jsx("option",{value:"groq",children:"Groq"}),y.jsx("option",{value:"deepseek",children:"DeepSeek"}),y.jsx("option",{value:"mistral",children:"Mistral"}),y.jsx("option",{value:"moonshot",children:"Moonshot AI"}),y.jsx("option",{value:"openrouter",children:"OpenRouter"}),y.jsx("option",{value:"perplexity",children:"Perplexity"}),y.jsx("option",{value:"qwen",children:"Alibaba Qwen"}),y.jsx("option",{value:"together",children:"Together AI"}),y.jsx("option",{value:"vertex",children:"Google Vertex AI"}),y.jsx("option",{value:"xai",children:"xAI"}),y.jsx("option",{value:"ollama",children:"Ollama"}),y.jsx("option",{value:"lmstudio",children:"LM Studio"})]})]}),r&&y.jsx(r,{onClose:n})]}),y.jsx("button",{onClick:n,className:"mt-4 w-full bg-adobe-accent text-white py-1 rounded",children:"Cancel"})]})})},a0=()=>{const{modal:e}=ca();if(!e)return null;switch(e){case"provider":return y.jsx(s0,{});case"settings":return null;case"chat-history":return null;default:return null}},u0=()=>y.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans",children:[y.jsx(Ig,{}),y.jsx("main",{className:"flex-1 overflow-y-auto px-3 py-2",children:y.jsx($v,{})}),y.jsx(Uv,{}),y.jsx(a0,{})]});dl.createRoot(document.getElementById("root")).render(y.jsx(ys.StrictMode,{children:y.jsx(u0,{})}));
