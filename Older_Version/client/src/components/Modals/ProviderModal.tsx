import React from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { useModalStore } from '../stores/modalStore';
import { OpenAIProvider } from '../../providers/openai';
import { OllamaProvider } from '../../providers/ollama';
import { LMStudioProvider } from '../../providers/lmstudio';
import { AnthropicProvider } from '../../providers/anthropic';
import { DeepSeekProvider } from '../../providers/deepseek';
import { GeminiProvider } from '../../providers/gemini';
import { GroqProvider } from '../../providers/groq';
import { MistralProvider } from '../../providers/mistral';
import { MoonshotProvider } from '../../providers/moonshot';
import { OpenRouterProvider } from '../../providers/openrouter';
import { PerplexityProvider } from '../../providers/perplexity';
import { QwenProvider } from '../../providers/qwen';
import { TogetherProvider } from '../../providers/together';
import { VertexProvider } from '../../providers/vertex';
import { XAIProvider } from '../../providers/xai';

const providerMap = {
  openai: OpenAIProvider,
  anthropic: AnthropicProvider,
  gemini: GeminiProvider,
  groq: GroqProvider,
  deepseek: DeepSeekProvider,
  mistral: MistralProvider,
  moonshot: MoonshotProvider,
  openrouter: OpenRouterProvider,
  perplexity: PerplexityProvider,
  qwen: QwenProvider,
  together: TogetherProvider,
  vertex: VertexProvider,
  xai: XAIProvider,
  ollama: OllamaProvider,
  lmstudio: LMStudioProvider,
};

export const ProviderModal: React.FC = () => {
  const { activeProvider, setProvider } = useSettingsStore();
  const { closeModal } = useModalStore();
  const ProviderComponent = providerMap[activeProvider as keyof typeof providerMap];

  const handleProviderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setProvider(e.target.value);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-md w-[380px] max-h-[550px] overflow-y-auto p-4">
        <div className="space-y-4">
          {/* Provider Selection Dropdown */}
          <div>
            <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
              Select Provider
            </label>
            <select
              value={activeProvider}
              onChange={handleProviderChange}
              className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none"
            >
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
              <option value="gemini">Google Gemini</option>
              <option value="groq">Groq</option>
              <option value="deepseek">DeepSeek</option>
              <option value="mistral">Mistral</option>
              <option value="moonshot">Moonshot AI</option>
              <option value="openrouter">OpenRouter</option>
              <option value="perplexity">Perplexity</option>
              <option value="qwen">Alibaba Qwen</option>
              <option value="together">Together AI</option>
              <option value="vertex">Google Vertex AI</option>
              <option value="xai">xAI</option>
              <option value="ollama">Ollama</option>
              <option value="lmstudio">LM Studio</option>
            </select>
          </div>

          {/* Provider Configuration */}
          {ProviderComponent && <ProviderComponent onClose={closeModal} />}
        </div>

        <button onClick={closeModal} className="mt-4 w-full bg-adobe-accent text-white py-1 rounded">
          Cancel
        </button>
      </div>
    </div>
  );
};