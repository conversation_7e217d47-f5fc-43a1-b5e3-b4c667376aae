import React from 'react';
import { Message } from '../../stores/chatStore';
import { ShikiCodeBlock } from './ShikiCodeBlock';

interface ChatMessageProps {
  message: Message;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isUser = message.role === 'user';

  return (
    <div className={`flex gap-3 ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div
        className={`max-w-[85%] rounded-lg px-3 py-2 text-sm leading-relaxed ${
          isUser
            ? 'bg-adobe-bg-tertiary text-adobe-text-primary'
            : 'text-adobe-text-primary'
        }`}
      >
        <ShikiCodeBlock content={message.content} />
      </div>
    </div>
  );
};