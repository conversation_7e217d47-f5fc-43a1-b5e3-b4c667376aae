import React from 'react';
import { useSettingsStore } from '../stores/settingsStore';

export const ProviderStatusIndicator: React.FC = () => {
  const { currentProvider } = useSettingsStore();

  if (!currentProvider) {
    return (
      <div className="w-2 h-2 rounded-full bg-adobe-text-secondary/50" title="No provider selected" />
    );
  }

  const isConfigured = currentProvider.isConfigured;

  return (
    <div 
      className={`w-2 h-2 rounded-full ${
        isConfigured 
          ? 'bg-green-500' 
          : 'bg-yellow-500'
      }`}
      title={
        isConfigured 
          ? `${currentProvider.name} is configured` 
          : `${currentProvider.name} needs configuration`
      }
    />
  );
};
