import { create } from 'zustand';

export type Message = {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
};

interface ChatState {
  messages: Message[];
  isLoading: boolean;
  currentSession?: string;
  addMessage: (msg: Omit<Message, 'id' | 'timestamp'>) => void;
  setLoading: (val: boolean) => void;
  createNewSession: () => void;
}

export const useChatStore = create<ChatState>((set) => ({
  messages: [],
  isLoading: false,

  addMessage: (msg) =>
    set((state) => ({
      messages: [
        ...state.messages,
        { ...msg, id: crypto.randomUUID(), timestamp: Date.now() },
      ],
    })),

  setLoading: (val) => set({ isLoading: val }),
  createNewSession: () => set({ messages: [], currentSession: crypto.randomUUID() }),
}));