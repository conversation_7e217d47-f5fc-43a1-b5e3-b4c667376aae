import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type Provider = {
  id: string;
  name: string;
  isConfigured: boolean;
  apiKey?: string;
  baseURL?: string;
  settings?: Record<string, unknown>;
};

type Model = {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  capabilities?: string[];
};

interface SettingsState {
  providers: Provider[];
  currentProvider?: Provider;
  currentModel?: Model;
  activeProvider: string;
  setProvider: (id: string) => void;
  setModel: (id: string) => void;
  updateProviderKey: (id: string, key: string) => void;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      providers: [
        { id: 'openai', name: 'OpenAI', isConfigured: false },
        { id: 'anthropic', name: 'Anthropic', isConfigured: false },
        { id: 'gemini', name: 'Google Gemini', isConfigured: false },
        { id: 'groq', name: '<PERSON><PERSON><PERSON>', isConfigured: false },
        { id: 'deepseek', name: '<PERSON><PERSON><PERSON>', isConfigured: false },
        { id: 'mistral', name: '<PERSON><PERSON><PERSON>', isConfigured: false },
        { id: 'moonshot', name: 'Moonshot AI', isConfigured: false },
        { id: 'openrouter', name: 'OpenRouter', isConfigured: false },
        { id: 'perplexity', name: 'Perplexity', isConfigured: false },
        { id: 'qwen', name: 'Alibaba Qwen', isConfigured: false },
        { id: 'together', name: 'Together AI', isConfigured: false },
        { id: 'vertex', name: 'Google Vertex AI', isConfigured: false },
        { id: 'xai', name: 'xAI', isConfigured: false },
        { id: 'ollama', name: 'Ollama', isConfigured: false },
        { id: 'lmstudio', name: 'LM Studio', isConfigured: false },
      ],
      activeProvider: 'openai',
      setProvider: (id) => {
        const provider = get().providers.find((p) => p.id === id);
        set({ currentProvider: provider, activeProvider: id });
      },
      setModel: (id) => {
        const model = { id, name: id }; // placeholder
        set({ currentModel: model });
      },
      updateProviderKey: (id, key) =>
        set((state) => ({
          providers: state.providers.map((p) =>
            p.id === id ? { ...p, apiKey: key, isConfigured: true } : p
          ),
        })),
    }),
    { name: 'sahai-settings' }
  )
);