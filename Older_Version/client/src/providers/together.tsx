import React, { useState } from 'react';
import { useSettingsStore } from '../components/stores/settingsStore';

export const TogetherProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  // Together does not expose a public /models endpoint, so we hard-code popular ones
  const models = [
    { id: 'meta-llama/Llama-3.2-70B-Instruct-Turbo', name: 'Llama 3.2 70B Turbo' },
    { id: 'mistralai/Mixtral-8x7B-Instruct-v0.1', name: 'Mixtral 8x7B Instruct' },
    { id: 'Qwen/Qwen2.5-Coder-32B-Instruct', name: 'Qwen2.5 Coder 32B' },
  ];

  return (
    <div className="space-y-4">
      {/* Select Model Section */}
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Select Model
        </label>
        <select
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
          onChange={(e) => updateProviderKey('together', key, e.target.value)}
        >
          {models.map((m) => (
            <option key={m.id} value={m.id}>{m.name}</option>
          ))}
        </select>
      </div>

      {/* Input API Key Section */}
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Input API Key
        </label>
        <input
          type="password"
          placeholder="..."
          value={key}
          onChange={(e) => setKey(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        />
      </div>

      {/* Save & Close Button */}
      <button
        onClick={() => { updateProviderKey('together', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};