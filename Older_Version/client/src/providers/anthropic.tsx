import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../components/stores/settingsStore';

export const AnthropicProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProv<PERSON><PERSON>ey } = useSettingsStore();
  const [key, setKey] = useState('');
  const [models] = useState([
    { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet' },
    { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus' },
    { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku' },
  ]);

  return (
    <div className="space-y-4">
      {/* Select Model Section */}
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Select Model
        </label>
        <select
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
          onChange={(e) => updateProviderKey('anthropic', key, e.target.value)}
        >
          {models.map((m) => (
            <option key={m.id} value={m.id}>{m.name}</option>
          ))}
        </select>
      </div>

      {/* Input API Key Section */}
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Input API Key
        </label>
        <input
          type="password"
          placeholder="sk-ant-..."
          value={key}
          onChange={(e) => setKey(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        />
      </div>

      {/* Save & Close Button */}
      <button
        onClick={() => { updateProviderKey('anthropic', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};