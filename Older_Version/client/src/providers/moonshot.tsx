import React, { useState } from 'react';
import { useSettingsStore } from '../components/stores/settingsStore';

export const MoonshotProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const models = [
    { id: 'moonshot-v1-8k', name: '<PERSON><PERSON> 8k' },
    { id: 'moonshot-v1-32k', name: '<PERSON><PERSON> 32k' },
  ];

  return (
    <div className="space-y-4">
      {/* Select Model Section */}
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Select Model
        </label>
        <select
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
          onChange={(e) => updateProviderKey('moonshot', key, e.target.value)}
        >
          {models.map((m) => (
            <option key={m.id} value={m.id}>{m.name}</option>
          ))}
        </select>
      </div>

      {/* Input API Key Section */}
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Input API Key
        </label>
        <input
          type="password"
          placeholder="sk-..."
          value={key}
          onChange={(e) => setKey(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        />
      </div>

      {/* Save & Close Button */}
      <button
        onClick={() => { updateProviderKey('moonshot', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};