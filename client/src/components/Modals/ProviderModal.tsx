import React, { useState, useEffect, useRef } from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { useModalStore } from '../stores/modalStore';
import { OpenAIProvider } from '../../providers/openai';
import { OllamaProvider } from '../../providers/ollama';
import { LMStudioProvider } from '../../providers/lmstudio';
import { AnthropicProvider } from '../../providers/anthropic';
import { DeepSeekProvider } from '../../providers/deepseek';
import { GeminiProvider } from '../../providers/gemini';
import { GroqProvider } from '../../providers/groq';
import { MistralProvider } from '../../providers/mistral';
import { MoonshotProvider } from '../../providers/moonshot';
import { OpenRouterProvider } from '../../providers/openrouter';
import { PerplexityProvider } from '../../providers/perplexity';
import { QwenProvider } from '../../providers/qwen';
import { TogetherProvider } from '../../providers/together';
import { VertexProvider } from '../../providers/vertex';
import { XAIProvider } from '../../providers/xai';
import { Search } from 'lucide-react';

const providerMap = {
  openai: OpenAIProvider,
  anthropic: AnthropicProvider,
  gemini: GeminiProvider,
  groq: GroqProvider,
  deepseek: DeepSeekProvider,
  mistral: MistralProvider,
  moonshot: MoonshotProvider,
  openrouter: OpenRouterProvider,
  perplexity: PerplexityProvider,
  qwen: QwenProvider,
  together: TogetherProvider,
  vertex: VertexProvider,
  xai: XAIProvider,
  ollama: OllamaProvider,
  lmstudio: LMStudioProvider,
};

const providerOptions = [
  { value: 'openai', label: 'OpenAI' },
  { value: 'anthropic', label: 'Anthropic' },
  { value: 'gemini', label: 'Google Gemini' },
  { value: 'groq', label: 'Groq' },
  { value: 'deepseek', label: 'DeepSeek' },
  { value: 'mistral', label: 'Mistral' },
  { value: 'moonshot', label: 'Moonshot AI' },
  { value: 'openrouter', label: 'OpenRouter' },
  { value: 'perplexity', label: 'Perplexity' },
  { value: 'qwen', label: 'Alibaba Qwen' },
  { value: 'together', label: 'Together AI' },
  { value: 'vertex', label: 'Google Vertex AI' },
  { value: 'xai', label: 'xAI' },
  { value: 'ollama', label: 'Ollama' },
  { value: 'lmstudio', label: 'LM Studio' },
];

export const ProviderModal: React.FC = () => {
  const { activeProvider, setProvider } = useSettingsStore();
  const { closeModal } = useModalStore();
  const ProviderComponent = providerMap[activeProvider as keyof typeof providerMap];
  const [searchQuery, setSearchQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const filteredProviders = providerOptions.filter(provider =>
    provider.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleProviderSelect = (provider: string) => {
    setProvider(provider);
    setShowDropdown(false);
    setSearchQuery('');
  };

  const handleSearch = () => {
    if (filteredProviders.length > 0) {
      handleProviderSelect(filteredProviders[0].value);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-md w-[380px] max-h-[550px] overflow-y-auto p-4">
        <div className="space-y-4">
          {/* Searchable Provider Selection */}
          <div className="relative" ref={dropdownRef}>
            <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
              Select Provider
            </label>
            <div className="relative">
              <input
                ref={inputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setShowDropdown(true);
                }}
                onFocus={() => setShowDropdown(true)}
                onKeyDown={handleKeyDown}
                placeholder="Search providers..."
                className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none pr-10"
              />
              <button
                onClick={handleSearch}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary"
              >
                <Search size={18} />
              </button>
            </div>

            {showDropdown && (
              <div className="absolute z-10 mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-lg max-h-60 overflow-auto">
                {filteredProviders.length > 0 ? (
                  filteredProviders.map((provider) => (
                    <div
                      key={provider.value}
                      className={`px-4 py-2 cursor-pointer ${
                        activeProvider === provider.value
                          ? 'bg-adobe-accent text-white'
                          : 'hover:bg-adobe-bg-tertiary text-adobe-text-primary'
                      }`}
                      onClick={() => handleProviderSelect(provider.value)}
                    >
                      {provider.label}
                    </div>
                  ))
                ) : (
                  <div className="px-4 py-2 text-adobe-text-secondary">
                    No providers found
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Provider Configuration */}
          {ProviderComponent && <ProviderComponent onClose={closeModal} />}
        </div>

        <button
          onClick={closeModal}
          className="mt-4 w-full bg-adobe-accent text-white py-1 rounded"
        >
          Cancel
        </button>
      </div>
    </div>
  );
};