import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useChatStore } from '../stores/chatStore';
import { Paperclip, Send, Mic, Loader2 } from 'lucide-react';

interface InputAreaProps {
  onAttachFile?: () => void;
  onVoiceInput?: () => void;
  onContextReference?: () => void;
}

const InputArea: React.FC<InputAreaProps> = React.memo(({
  onAttachFile,
  onVoiceInput,
  onContextReference
}) => {
  const [input, setInput] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { addMessage, isLoading, setLoading, currentSession, createNewSession } = useChatStore();

  const maxChars = 4000;
  const isEmpty = !input.trim();
  const isNearLimit = input.length > maxChars * 0.9;

  // Set initial height and handle resizing
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = '72px'; // 3 lines minimum
    }
  }, []);

  const resizeTextarea = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;
    
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(Math.max(textarea.scrollHeight, 72), 200)}px`;
  }, []);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
    resizeTextarea();
  }, [resizeTextarea]);

  const handleSend = useCallback(async () => {
    const text = input.trim();
    if (!text || isLoading) return;

    setInput('');
    if (textareaRef.current) {
      textareaRef.current.style.height = '72px';
    }

    try {
      setLoading(true);
      if (!currentSession) createNewSession();
      addMessage({ content: text, role: 'user' });

      setTimeout(() => {
        addMessage({ content: `Echo: ${text}`, role: 'assistant' });
        setLoading(false);
      }, 1000);
    } catch (err) {
      setInput(text);
      setLoading(false);
    }
  }, [input, isLoading, currentSession, addMessage, setLoading, createNewSession]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
        e.preventDefault();
        handleSend();
      }
    },
    [handleSend, isComposing]
  );

  return (
    <div className="px-4 pb-3 pt-2 bg-adobe-bg-secondary border-t border-adobe-border">
      <div className="relative flex items-end bg-adobe-bg-tertiary rounded-lg border border-adobe-border focus-within:border-adobe-accent focus-within:ring-1 focus-within:ring-adobe-accent transition-colors">
        <div className="flex items-center pl-2 pb-1 self-end">
          <button
            onClick={onAttachFile}
            className="text-adobe-text-secondary hover:text-adobe-accent transition p-1 rounded"
            title="Attach file"
            disabled={isLoading}
          >
            <Paperclip size={18} />
          </button>
        </div>

        <textarea
          ref={textareaRef}
          rows={3}
          maxLength={maxChars}
          value={input}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onCompositionStart={() => setIsComposing(true)}
          onCompositionEnd={() => setIsComposing(false)}
          placeholder="Type a message..."
          className="flex-1 resize-none bg-transparent text-adobe-text-primary text-sm p-2 outline-none placeholder:text-adobe-text-secondary/80
            min-h-[72px] max-h-[200px] leading-relaxed overflow-hidden"
        />

        <div className="flex items-center pr-2 pb-1 space-x-1 self-end">
          <button
            onClick={onVoiceInput}
            className="text-adobe-text-secondary hover:text-adobe-warning transition p-1 rounded disabled:opacity-40"
            title="Voice input"
            disabled={isLoading}
          >
            <Mic size={18} />
          </button>
          <button
            onClick={handleSend}
            disabled={isEmpty || isLoading}
            className="text-adobe-accent hover:text-adobe-accent-hover transition p-1 rounded disabled:text-adobe-text-secondary/50 disabled:hover:text-adobe-text-secondary/50"
            title="Send"
          >
            {isLoading ? <Loader2 size={18} className="animate-spin" /> : <Send size={18} />}
          </button>
        </div>
      </div>

      <div className="flex justify-between items-center mt-1 px-1">
        <span className={`text-xs ${isNearLimit ? 'text-adobe-warning' : 'text-adobe-text-secondary'}`}>
          {input.length}/{maxChars}
        </span>
        <span className="text-xs text-adobe-text-secondary">
          Enter to send, Shift+Enter for new line
        </span>
      </div>
    </div>
  );
});

export default InputArea;